"""
Tools package for AI Assistant Function Calling
Auto-discovers and manages all available tools
"""

import os
import importlib
from typing import List, Dict, Any
from .base_tool import BaseTool


def get_all_tools() -> List[BaseTool]:
    """
    Auto-discover and return all available tool instances.
    
    Returns:
        List of instantiated tool objects
    """
    tools = []
    tools_dir = os.path.dirname(__file__)
    
    # Get all Python files in the tools directory
    for filename in os.listdir(tools_dir):
        if filename.endswith('_tool.py') and filename != 'base_tool.py':
            module_name = filename[:-3]  # Remove .py extension
            
            try:
                # Import the module
                module = importlib.import_module(f'tools.{module_name}')
                
                # Find classes that inherit from BaseTool
                for attr_name in dir(module):
                    attr = getattr(module, attr_name)
                    if (isinstance(attr, type) and 
                        issubclass(attr, BaseTool) and 
                        attr != BaseTool):
                        # Instantiate the tool
                        tool_instance = attr()
                        tools.append(tool_instance)
                        
            except Exception as e:
                print(f"Warning: Could not load tool from {filename}: {e}")
    
    return tools


def get_tool_by_name(name: str) -> BaseTool:
    """
    Get a specific tool by its name.
    
    Args:
        name: The name of the tool to retrieve
        
    Returns:
        The tool instance if found, None otherwise
    """
    tools = get_all_tools()
    for tool in tools:
        if tool.name == name:
            return tool
    return None


def get_tools_summary() -> Dict[str, Any]:
    """
    Get a summary of all available tools.
    
    Returns:
        Dictionary with tool information
    """
    tools = get_all_tools()
    summary = {
        'count': len(tools),
        'tools': []
    }
    
    for tool in tools:
        tool_info = {
            'name': tool.name,
            'description': tool.description,
            'category': tool.category,
            'functions': [func['name'] for func in tool.get_declarations()]
        }
        summary['tools'].append(tool_info)
    
    return summary


# Export main functions
__all__ = ['get_all_tools', 'get_tool_by_name', 'get_tools_summary', 'BaseTool']
