"""
File Reader Tool for AI Assistant
Advanced file reading capabilities with support for multiple files and formats
"""

import os
import json
from typing import Dict, Any, List, Union
from .base_tool import BaseTool


class FileReaderTool(BaseTool):
    """Advanced tool for reading and analyzing files."""
    
    def __init__(self):
        super().__init__()
        self.name = "file_reader"
        self.category = "system"
        self.description = "Advanced file reading tool that can read single or multiple files, analyze content, and provide summaries"
    
    def get_declarations(self) -> List[Dict[str, Any]]:
        """Return function declarations for file reading operations."""
        return [
            {
                "name": "read_file",
                "description": "Read and return the complete contents of a single file. Supports text files, code files, and configuration files.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "file_path": {
                            "type": "string",
                            "description": "Path to the file to read (e.g., 'function_calling.txt', 'ai_assistant.py')"
                        },
                        "max_lines": {
                            "type": "integer",
                            "description": "Maximum number of lines to read (default: no limit)",
                            "minimum": 1
                        },
                        "encoding": {
                            "type": "string",
                            "description": "File encoding (default: 'utf-8')",
                            "enum": ["utf-8", "ascii", "latin-1"]
                        }
                    },
                    "required": ["file_path"]
                }
            },
            {
                "name": "read_multiple_files",
                "description": "Read and return contents of multiple files at once. Perfect for analyzing related files or documentation sets.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "file_paths": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "List of file paths to read (e.g., ['function_calling.txt', 'text_generation.txt'])"
                        },
                        "max_lines_per_file": {
                            "type": "integer",
                            "description": "Maximum lines to read per file (default: no limit)",
                            "minimum": 1
                        },
                        "include_metadata": {
                            "type": "boolean",
                            "description": "Include file metadata (size, modification time, etc.)"
                        }
                    },
                    "required": ["file_paths"]
                }
            },
            {
                "name": "analyze_file_content",
                "description": "Read a file and provide analysis including line count, file type, and content summary.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "file_path": {
                            "type": "string",
                            "description": "Path to the file to analyze"
                        },
                        "analysis_type": {
                            "type": "string",
                            "enum": ["basic", "detailed", "code_analysis"],
                            "description": "Type of analysis to perform"
                        }
                    },
                    "required": ["file_path"]
                }
            },
            {
                "name": "search_in_files",
                "description": "Search for specific text or patterns across multiple files.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "file_paths": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "List of files to search in"
                        },
                        "search_term": {
                            "type": "string",
                            "description": "Text or pattern to search for"
                        },
                        "case_sensitive": {
                            "type": "boolean",
                            "description": "Whether search should be case sensitive (default: false)"
                        },
                        "max_results": {
                            "type": "integer",
                            "description": "Maximum number of results to return (default: 10)"
                        }
                    },
                    "required": ["file_paths", "search_term"]
                }
            }
        ]
    
    def execute(self, function_name: str, **kwargs) -> Dict[str, Any]:
        """Execute file reader functions."""
        try:
            if function_name == "read_file":
                return self._read_file(**kwargs)
            elif function_name == "read_multiple_files":
                return self._read_multiple_files(**kwargs)
            elif function_name == "analyze_file_content":
                return self._analyze_file_content(**kwargs)
            elif function_name == "search_in_files":
                return self._search_in_files(**kwargs)
            else:
                raise ValueError(f"Unknown function: {function_name}")
                
        except Exception as e:
            return self.handle_error(e, function_name)
    
    def _read_file(self, file_path: str, max_lines: int = None, encoding: str = "utf-8") -> Dict[str, Any]:
        """Read a single file completely."""
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
        
        if not os.path.isfile(file_path):
            raise ValueError(f"Path is not a file: {file_path}")
        
        try:
            with open(file_path, 'r', encoding=encoding) as file:
                if max_lines:
                    lines = []
                    for i, line in enumerate(file):
                        if i >= max_lines:
                            break
                        lines.append(line.rstrip('\n\r'))
                    content = '\n'.join(lines)
                    truncated = i >= max_lines
                else:
                    content = file.read()
                    lines = content.split('\n')
                    truncated = False
                
                # Get file metadata
                file_size = os.path.getsize(file_path)
                file_stat = os.stat(file_path)
                
                result = {
                    "file_path": file_path,
                    "content": content,
                    "line_count": len(lines),
                    "character_count": len(content),
                    "file_size": file_size,
                    "file_size_formatted": self._format_file_size(file_size),
                    "encoding": encoding,
                    "truncated": truncated,
                    "max_lines_read": max_lines if max_lines else len(lines),
                    "file_extension": os.path.splitext(file_path)[1],
                    "modification_time": file_stat.st_mtime
                }
                
                return self.format_success_response(result, "read_file")
                
        except UnicodeDecodeError:
            raise ValueError(f"Cannot decode file with {encoding} encoding: {file_path}")
        except PermissionError:
            raise PermissionError(f"Permission denied reading file: {file_path}")
    
    def _read_multiple_files(self, file_paths: List[str], max_lines_per_file: int = None, 
                           include_metadata: bool = True) -> Dict[str, Any]:
        """Read multiple files at once."""
        results = []
        total_lines = 0
        total_size = 0
        errors = []
        
        for file_path in file_paths:
            try:
                file_result = self._read_file(file_path, max_lines_per_file)
                file_data = file_result['result']
                
                # Simplify data if metadata not requested
                if not include_metadata:
                    file_data = {
                        "file_path": file_data["file_path"],
                        "content": file_data["content"],
                        "line_count": file_data["line_count"]
                    }
                
                results.append(file_data)
                total_lines += file_data["line_count"]
                total_size += file_data.get("file_size", 0)
                
            except Exception as e:
                error_info = {
                    "file_path": file_path,
                    "error": str(e),
                    "error_type": type(e).__name__
                }
                errors.append(error_info)
        
        summary = {
            "files_read": len(results),
            "files_requested": len(file_paths),
            "total_lines": total_lines,
            "total_size": total_size,
            "total_size_formatted": self._format_file_size(total_size),
            "files": results,
            "errors": errors,
            "success_rate": len(results) / len(file_paths) if file_paths else 0
        }
        
        return self.format_success_response(summary, "read_multiple_files")
    
    def _analyze_file_content(self, file_path: str, analysis_type: str = "basic") -> Dict[str, Any]:
        """Analyze file content and provide insights."""
        file_result = self._read_file(file_path)
        file_data = file_result['result']
        content = file_data['content']
        
        analysis = {
            "file_path": file_path,
            "basic_stats": {
                "line_count": file_data['line_count'],
                "character_count": file_data['character_count'],
                "word_count": len(content.split()),
                "file_size": file_data['file_size_formatted'],
                "file_extension": file_data['file_extension']
            }
        }
        
        if analysis_type in ["detailed", "code_analysis"]:
            # Detailed analysis
            lines = content.split('\n')
            non_empty_lines = [line for line in lines if line.strip()]
            
            analysis["detailed_stats"] = {
                "empty_lines": len(lines) - len(non_empty_lines),
                "non_empty_lines": len(non_empty_lines),
                "average_line_length": sum(len(line) for line in lines) / len(lines) if lines else 0,
                "longest_line_length": max(len(line) for line in lines) if lines else 0,
                "shortest_line_length": min(len(line) for line in lines) if lines else 0
            }
            
            # Content type detection
            file_ext = file_data['file_extension'].lower()
            if file_ext in ['.py', '.js', '.java', '.cpp', '.c', '.h']:
                analysis["content_type"] = "code"
            elif file_ext in ['.txt', '.md', '.rst']:
                analysis["content_type"] = "text/documentation"
            elif file_ext in ['.json', '.xml', '.yaml', '.yml']:
                analysis["content_type"] = "configuration/data"
            else:
                analysis["content_type"] = "unknown"
        
        if analysis_type == "code_analysis" and analysis.get("content_type") == "code":
            # Code-specific analysis
            analysis["code_stats"] = self._analyze_code_content(content, file_data['file_extension'])
        
        return self.format_success_response(analysis, "analyze_file_content")
    
    def _analyze_code_content(self, content: str, file_extension: str) -> Dict[str, Any]:
        """Analyze code-specific metrics."""
        lines = content.split('\n')
        
        # Basic code metrics
        comment_lines = 0
        import_lines = 0
        function_lines = 0
        class_lines = 0
        
        for line in lines:
            stripped = line.strip()
            if not stripped:
                continue
                
            # Python-specific analysis
            if file_extension == '.py':
                if stripped.startswith('#'):
                    comment_lines += 1
                elif stripped.startswith(('import ', 'from ')):
                    import_lines += 1
                elif stripped.startswith('def '):
                    function_lines += 1
                elif stripped.startswith('class '):
                    class_lines += 1
            
            # JavaScript-specific analysis
            elif file_extension == '.js':
                if stripped.startswith('//') or stripped.startswith('/*'):
                    comment_lines += 1
                elif 'function' in stripped or '=>' in stripped:
                    function_lines += 1
                elif stripped.startswith('class '):
                    class_lines += 1
        
        return {
            "comment_lines": comment_lines,
            "import_lines": import_lines,
            "function_definitions": function_lines,
            "class_definitions": class_lines,
            "code_to_comment_ratio": (len(lines) - comment_lines) / max(comment_lines, 1)
        }
    
    def _search_in_files(self, file_paths: List[str], search_term: str, 
                        case_sensitive: bool = False, max_results: int = 10) -> Dict[str, Any]:
        """Search for text across multiple files."""
        results = []
        total_matches = 0
        
        for file_path in file_paths:
            try:
                file_result = self._read_file(file_path)
                content = file_result['result']['content']
                lines = content.split('\n')
                
                file_matches = []
                search_text = search_term if case_sensitive else search_term.lower()
                
                for line_num, line in enumerate(lines, 1):
                    check_line = line if case_sensitive else line.lower()
                    if search_text in check_line:
                        match = {
                            "line_number": line_num,
                            "line_content": line.strip(),
                            "match_position": check_line.find(search_text)
                        }
                        file_matches.append(match)
                        total_matches += 1
                        
                        if len(results) >= max_results:
                            break
                
                if file_matches:
                    results.append({
                        "file_path": file_path,
                        "matches": file_matches[:max_results],
                        "total_matches_in_file": len(file_matches)
                    })
                    
            except Exception as e:
                # Skip files that can't be read
                continue
        
        search_summary = {
            "search_term": search_term,
            "case_sensitive": case_sensitive,
            "files_searched": len(file_paths),
            "files_with_matches": len(results),
            "total_matches": total_matches,
            "results": results[:max_results]
        }
        
        return self.format_success_response(search_summary, "search_in_files")
    
    def _format_file_size(self, size_bytes: int) -> str:
        """Format file size in human-readable format."""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        size = float(size_bytes)
        
        while size >= 1024.0 and i < len(size_names) - 1:
            size /= 1024.0
            i += 1
        
        return f"{size:.1f} {size_names[i]}"
    
    def get_python_functions(self) -> List[callable]:
        """Return Python functions for automatic function calling."""
        
        def read_file(file_path: str, max_lines: int = None, encoding: str = "utf-8") -> Dict[str, Any]:
            """Read and return the complete contents of a single file.
            
            Args:
                file_path: Path to the file to read
                max_lines: Maximum number of lines to read
                encoding: File encoding
                
            Returns:
                Dictionary containing file contents and metadata
            """
            result = self._read_file(file_path, max_lines, encoding)
            return result.get('result', {})
        
        def read_multiple_files(file_paths: List[str], max_lines_per_file: int = None, 
                              include_metadata: bool = True) -> Dict[str, Any]:
            """Read and return contents of multiple files at once.
            
            Args:
                file_paths: List of file paths to read
                max_lines_per_file: Maximum lines to read per file
                include_metadata: Include file metadata
                
            Returns:
                Dictionary containing all file contents and summary
            """
            result = self._read_multiple_files(file_paths, max_lines_per_file, include_metadata)
            return result.get('result', {})
        
        def analyze_file_content(file_path: str, analysis_type: str = "basic") -> Dict[str, Any]:
            """Analyze file content and provide insights.
            
            Args:
                file_path: Path to the file to analyze
                analysis_type: Type of analysis ('basic', 'detailed', 'code_analysis')
                
            Returns:
                Dictionary containing file analysis
            """
            result = self._analyze_file_content(file_path, analysis_type)
            return result.get('result', {})
        
        def search_in_files(file_paths: List[str], search_term: str, 
                          case_sensitive: bool = False, max_results: int = 10) -> Dict[str, Any]:
            """Search for specific text across multiple files.
            
            Args:
                file_paths: List of files to search in
                search_term: Text to search for
                case_sensitive: Whether search should be case sensitive
                max_results: Maximum number of results to return
                
            Returns:
                Dictionary containing search results
            """
            result = self._search_in_files(file_paths, search_term, case_sensitive, max_results)
            return result.get('result', {})
        
        return [read_file, read_multiple_files, analyze_file_content, search_in_files]
