"""
Calculator Tool for AI Assistant
Provides mathematical calculation capabilities
"""

import re
import math
from typing import Dict, Any, List
from .base_tool import BaseTool


class CalculatorTool(BaseTool):
    """Tool for performing mathematical calculations."""
    
    def __init__(self):
        super().__init__()
        self.name = "calculator"
        self.category = "computation"
        self.description = "Perform mathematical calculations and operations"
    
    def get_declarations(self) -> List[Dict[str, Any]]:
        """Return function declarations for calculator operations."""
        return [
            {
                "name": "calculate",
                "description": "Evaluates a mathematical expression and returns the result. Supports basic arithmetic, trigonometry, and common math functions.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "expression": {
                            "type": "string",
                            "description": "Mathematical expression to evaluate (e.g., '2 + 3 * 4', 'sin(pi/2)', 'sqrt(16)')"
                        }
                    },
                    "required": ["expression"]
                }
            },
            {
                "name": "convert_units",
                "description": "Convert between different units of measurement.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "value": {
                            "type": "number",
                            "description": "The numeric value to convert"
                        },
                        "from_unit": {
                            "type": "string",
                            "description": "Source unit (e.g., 'celsius', 'fahrenheit', 'meters', 'feet')"
                        },
                        "to_unit": {
                            "type": "string",
                            "description": "Target unit (e.g., 'celsius', 'fahrenheit', 'meters', 'feet')"
                        }
                    },
                    "required": ["value", "from_unit", "to_unit"]
                }
            }
        ]
    
    def execute(self, function_name: str, **kwargs) -> Dict[str, Any]:
        """Execute calculator functions."""
        try:
            if function_name == "calculate":
                return self._calculate(**kwargs)
            elif function_name == "convert_units":
                return self._convert_units(**kwargs)
            else:
                raise ValueError(f"Unknown function: {function_name}")
                
        except Exception as e:
            return self.handle_error(e, function_name)
    
    def _calculate(self, expression: str) -> Dict[str, Any]:
        """Safely evaluate a mathematical expression."""
        # Clean the expression
        expression = expression.strip()
        
        # Replace common math functions and constants
        replacements = {
            'pi': 'math.pi',
            'e': 'math.e',
            'sin': 'math.sin',
            'cos': 'math.cos',
            'tan': 'math.tan',
            'sqrt': 'math.sqrt',
            'log': 'math.log',
            'log10': 'math.log10',
            'abs': 'abs',
            'pow': 'pow',
            'round': 'round'
        }
        
        # Apply replacements (be careful with word boundaries)
        for old, new in replacements.items():
            expression = re.sub(r'\b' + old + r'\b', new, expression)
        
        # Validate expression (only allow safe characters)
        allowed_chars = set('0123456789+-*/().abcdefghijklmnopqrstuvwxyz_')
        if not all(c.lower() in allowed_chars or c.isspace() for c in expression):
            raise ValueError("Expression contains invalid characters")
        
        # Evaluate safely
        try:
            # Create a safe namespace
            safe_dict = {
                "__builtins__": {},
                "math": math,
                "abs": abs,
                "pow": pow,
                "round": round
            }
            
            result = eval(expression, safe_dict)
            
            # Format result
            if isinstance(result, float):
                if result.is_integer():
                    result = int(result)
                else:
                    result = round(result, 10)  # Limit decimal places
            
            calculation_data = {
                "expression": expression,
                "result": result,
                "formatted_result": str(result)
            }
            
            return self.format_success_response(calculation_data, "calculate")
            
        except ZeroDivisionError:
            raise ValueError("Division by zero")
        except OverflowError:
            raise ValueError("Result too large")
        except ValueError as e:
            raise ValueError(f"Invalid expression: {str(e)}")
    
    def _convert_units(self, value: float, from_unit: str, to_unit: str) -> Dict[str, Any]:
        """Convert between different units."""
        from_unit = from_unit.lower()
        to_unit = to_unit.lower()
        
        # Temperature conversions
        if from_unit in ['celsius', 'fahrenheit', 'kelvin'] and to_unit in ['celsius', 'fahrenheit', 'kelvin']:
            result = self._convert_temperature(value, from_unit, to_unit)
        
        # Length conversions
        elif from_unit in ['meters', 'feet', 'inches', 'centimeters', 'kilometers', 'miles'] and to_unit in ['meters', 'feet', 'inches', 'centimeters', 'kilometers', 'miles']:
            result = self._convert_length(value, from_unit, to_unit)
        
        # Weight conversions
        elif from_unit in ['kilograms', 'pounds', 'grams', 'ounces'] and to_unit in ['kilograms', 'pounds', 'grams', 'ounces']:
            result = self._convert_weight(value, from_unit, to_unit)
        
        else:
            raise ValueError(f"Conversion from {from_unit} to {to_unit} not supported")
        
        conversion_data = {
            "original_value": value,
            "from_unit": from_unit,
            "to_unit": to_unit,
            "converted_value": result,
            "formatted_result": f"{value} {from_unit} = {result} {to_unit}"
        }
        
        return self.format_success_response(conversion_data, "convert_units")
    
    def _convert_temperature(self, value: float, from_unit: str, to_unit: str) -> float:
        """Convert temperature between different units."""
        # Convert to Celsius first
        if from_unit == 'fahrenheit':
            celsius = (value - 32) * 5/9
        elif from_unit == 'kelvin':
            celsius = value - 273.15
        else:  # celsius
            celsius = value
        
        # Convert from Celsius to target unit
        if to_unit == 'fahrenheit':
            return round(celsius * 9/5 + 32, 2)
        elif to_unit == 'kelvin':
            return round(celsius + 273.15, 2)
        else:  # celsius
            return round(celsius, 2)
    
    def _convert_length(self, value: float, from_unit: str, to_unit: str) -> float:
        """Convert length between different units."""
        # Convert to meters first
        to_meters = {
            'meters': 1,
            'feet': 0.3048,
            'inches': 0.0254,
            'centimeters': 0.01,
            'kilometers': 1000,
            'miles': 1609.34
        }
        
        meters = value * to_meters[from_unit]
        result = meters / to_meters[to_unit]
        return round(result, 6)
    
    def _convert_weight(self, value: float, from_unit: str, to_unit: str) -> float:
        """Convert weight between different units."""
        # Convert to kilograms first
        to_kg = {
            'kilograms': 1,
            'pounds': 0.453592,
            'grams': 0.001,
            'ounces': 0.0283495
        }
        
        kg = value * to_kg[from_unit]
        result = kg / to_kg[to_unit]
        return round(result, 6)
    
    def get_python_functions(self) -> List[callable]:
        """Return Python functions for automatic function calling."""
        
        def calculate(expression: str) -> Dict[str, Any]:
            """Evaluates a mathematical expression and returns the result.
            
            Args:
                expression: Mathematical expression to evaluate
                
            Returns:
                Dictionary containing calculation result
            """
            result = self._calculate(expression)
            return result.get('result', {})
        
        def convert_units(value: float, from_unit: str, to_unit: str) -> Dict[str, Any]:
            """Convert between different units of measurement.
            
            Args:
                value: The numeric value to convert
                from_unit: Source unit
                to_unit: Target unit
                
            Returns:
                Dictionary containing conversion result
            """
            result = self._convert_units(value, from_unit, to_unit)
            return result.get('result', {})
        
        return [calculate, convert_units]
