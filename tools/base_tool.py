"""
Base Tool Class for AI Assistant Function Calling
Provides abstract interface for all tools
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Union
import json


class BaseTool(ABC):
    """
    Abstract base class for all AI assistant tools.
    
    Each tool must implement:
    - get_declarations(): Return function declarations for the AI model
    - execute(): Execute the actual function with given parameters
    """
    
    def __init__(self):
        self.name = self.__class__.__name__.replace('Tool', '').lower()
        self.category = "general"
        self.description = "A tool for the AI assistant"
    
    @abstractmethod
    def get_declarations(self) -> List[Dict[str, Any]]:
        """
        Return function declarations in Gemini API format.
        
        Returns:
            List of function declaration dictionaries following OpenAPI schema
        """
        pass
    
    @abstractmethod
    def execute(self, function_name: str, **kwargs) -> Dict[str, Any]:
        """
        Execute a function with the given parameters.
        
        Args:
            function_name: Name of the function to execute
            **kwargs: Function parameters
            
        Returns:
            Dictionary containing the result or error information
        """
        pass
    
    def get_python_functions(self) -> List[callable]:
        """
        Return Python functions for automatic function calling.
        Override this method if you want to support automatic function calling.
        
        Returns:
            List of Python callable functions
        """
        return []
    
    def validate_parameters(self, function_name: str, parameters: Dict[str, Any]) -> bool:
        """
        Validate parameters for a function call.
        
        Args:
            function_name: Name of the function
            parameters: Parameters to validate
            
        Returns:
            True if parameters are valid, False otherwise
        """
        declarations = self.get_declarations()
        
        for declaration in declarations:
            if declaration['name'] == function_name:
                required_params = declaration.get('parameters', {}).get('required', [])
                
                # Check if all required parameters are present
                for param in required_params:
                    if param not in parameters:
                        return False
                
                return True
        
        return False
    
    def handle_error(self, error: Exception, function_name: str) -> Dict[str, Any]:
        """
        Handle errors that occur during function execution.
        
        Args:
            error: The exception that occurred
            function_name: Name of the function that failed
            
        Returns:
            Error response dictionary
        """
        return {
            'success': False,
            'error': str(error),
            'function': function_name,
            'message': f"Error executing {function_name}: {str(error)}"
        }
    
    def format_success_response(self, result: Any, function_name: str) -> Dict[str, Any]:
        """
        Format a successful function execution response.
        
        Args:
            result: The function execution result
            function_name: Name of the function that was executed
            
        Returns:
            Success response dictionary
        """
        return {
            'success': True,
            'result': result,
            'function': function_name,
            'message': f"Successfully executed {function_name}"
        }
    
    def __str__(self) -> str:
        """String representation of the tool."""
        return f"{self.__class__.__name__}(name='{self.name}', category='{self.category}')"
    
    def __repr__(self) -> str:
        """Detailed string representation of the tool."""
        return self.__str__()
