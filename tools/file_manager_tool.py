"""
File Manager Tool for AI Assistant
Provides file system operations
"""

import os
import json
from typing import Dict, Any, List
from .base_tool import BaseTool


class FileManagerTool(BaseTool):
    """Tool for file system operations."""
    
    def __init__(self):
        super().__init__()
        self.name = "file_manager"
        self.category = "system"
        self.description = "Perform file system operations like listing files and reading file contents"
    
    def get_declarations(self) -> List[Dict[str, Any]]:
        """Return function declarations for file operations."""
        return [
            {
                "name": "list_files",
                "description": "Lists files and directories in a specified directory path.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "directory": {
                            "type": "string",
                            "description": "Directory path to list (default: current directory)"
                        },
                        "show_hidden": {
                            "type": "boolean",
                            "description": "Whether to show hidden files (starting with .)"
                        }
                    },
                    "required": []
                }
            },
            {
                "name": "read_file",
                "description": "Reads and returns the contents of a text file.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "filename": {
                            "type": "string",
                            "description": "Path to the file to read"
                        },
                        "max_lines": {
                            "type": "integer",
                            "description": "Maximum number of lines to read (default: 100)"
                        }
                    },
                    "required": ["filename"]
                }
            },
            {
                "name": "get_file_info",
                "description": "Gets information about a file or directory (size, modification time, etc.).",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "path": {
                            "type": "string",
                            "description": "Path to the file or directory"
                        }
                    },
                    "required": ["path"]
                }
            }
        ]
    
    def execute(self, function_name: str, **kwargs) -> Dict[str, Any]:
        """Execute file manager functions."""
        try:
            if function_name == "list_files":
                return self._list_files(**kwargs)
            elif function_name == "read_file":
                return self._read_file(**kwargs)
            elif function_name == "get_file_info":
                return self._get_file_info(**kwargs)
            else:
                raise ValueError(f"Unknown function: {function_name}")
                
        except Exception as e:
            return self.handle_error(e, function_name)
    
    def _list_files(self, directory: str = ".", show_hidden: bool = False) -> Dict[str, Any]:
        """List files and directories."""
        if not os.path.exists(directory):
            raise FileNotFoundError(f"Directory not found: {directory}")
        
        if not os.path.isdir(directory):
            raise ValueError(f"Path is not a directory: {directory}")
        
        try:
            items = os.listdir(directory)
            
            # Filter hidden files if requested
            if not show_hidden:
                items = [item for item in items if not item.startswith('.')]
            
            # Categorize items
            files = []
            directories = []
            
            for item in sorted(items):
                item_path = os.path.join(directory, item)
                
                if os.path.isdir(item_path):
                    directories.append({
                        "name": item,
                        "type": "directory",
                        "size": self._get_directory_size(item_path)
                    })
                else:
                    file_size = os.path.getsize(item_path)
                    files.append({
                        "name": item,
                        "type": "file",
                        "size": file_size,
                        "size_formatted": self._format_file_size(file_size)
                    })
            
            listing_data = {
                "directory": os.path.abspath(directory),
                "total_items": len(files) + len(directories),
                "files_count": len(files),
                "directories_count": len(directories),
                "directories": directories,
                "files": files
            }
            
            return self.format_success_response(listing_data, "list_files")
            
        except PermissionError:
            raise PermissionError(f"Permission denied accessing directory: {directory}")
    
    def _read_file(self, filename: str, max_lines: int = 100) -> Dict[str, Any]:
        """Read file contents."""
        if not os.path.exists(filename):
            raise FileNotFoundError(f"File not found: {filename}")
        
        if not os.path.isfile(filename):
            raise ValueError(f"Path is not a file: {filename}")
        
        try:
            with open(filename, 'r', encoding='utf-8') as file:
                lines = []
                for i, line in enumerate(file):
                    if i >= max_lines:
                        break
                    lines.append(line.rstrip('\n\r'))
                
                content = '\n'.join(lines)
                
                # Get file info
                file_size = os.path.getsize(filename)
                
                file_data = {
                    "filename": filename,
                    "content": content,
                    "lines_read": len(lines),
                    "max_lines": max_lines,
                    "file_size": file_size,
                    "file_size_formatted": self._format_file_size(file_size),
                    "truncated": i >= max_lines
                }
                
                return self.format_success_response(file_data, "read_file")
                
        except UnicodeDecodeError:
            raise ValueError(f"File is not a text file or uses unsupported encoding: {filename}")
        except PermissionError:
            raise PermissionError(f"Permission denied reading file: {filename}")
    
    def _get_file_info(self, path: str) -> Dict[str, Any]:
        """Get file or directory information."""
        if not os.path.exists(path):
            raise FileNotFoundError(f"Path not found: {path}")
        
        stat_info = os.stat(path)
        
        info_data = {
            "path": os.path.abspath(path),
            "name": os.path.basename(path),
            "type": "directory" if os.path.isdir(path) else "file",
            "size": stat_info.st_size,
            "size_formatted": self._format_file_size(stat_info.st_size),
            "modified_time": stat_info.st_mtime,
            "created_time": stat_info.st_ctime,
            "permissions": oct(stat_info.st_mode)[-3:],
            "is_readable": os.access(path, os.R_OK),
            "is_writable": os.access(path, os.W_OK),
            "is_executable": os.access(path, os.X_OK)
        }
        
        return self.format_success_response(info_data, "get_file_info")
    
    def _get_directory_size(self, directory: str) -> int:
        """Get total size of directory (simplified)."""
        try:
            total_size = 0
            for dirpath, dirnames, filenames in os.walk(directory):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    try:
                        total_size += os.path.getsize(filepath)
                    except (OSError, FileNotFoundError):
                        pass
            return total_size
        except:
            return 0
    
    def _format_file_size(self, size_bytes: int) -> str:
        """Format file size in human-readable format."""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        size = float(size_bytes)
        
        while size >= 1024.0 and i < len(size_names) - 1:
            size /= 1024.0
            i += 1
        
        return f"{size:.1f} {size_names[i]}"
    
    def get_python_functions(self) -> List[callable]:
        """Return Python functions for automatic function calling."""
        
        def list_files(directory: str = ".", show_hidden: bool = False) -> Dict[str, Any]:
            """Lists files and directories in a specified directory.
            
            Args:
                directory: Directory path to list
                show_hidden: Whether to show hidden files
                
            Returns:
                Dictionary containing directory listing
            """
            result = self._list_files(directory, show_hidden)
            return result.get('result', {})
        
        def read_file(filename: str, max_lines: int = 100) -> Dict[str, Any]:
            """Reads and returns the contents of a text file.
            
            Args:
                filename: Path to the file to read
                max_lines: Maximum number of lines to read
                
            Returns:
                Dictionary containing file contents
            """
            result = self._read_file(filename, max_lines)
            return result.get('result', {})
        
        def get_file_info(path: str) -> Dict[str, Any]:
            """Gets information about a file or directory.
            
            Args:
                path: Path to the file or directory
                
            Returns:
                Dictionary containing file information
            """
            result = self._get_file_info(path)
            return result.get('result', {})
        
        return [list_files, read_file, get_file_info]
