"""
Weather Tool for AI Assistant
Provides weather information (mock implementation)
"""

import random
from typing import Dict, Any, List
from .base_tool import BaseTool


class WeatherTool(BaseTool):
    """Tool for getting weather information."""
    
    def __init__(self):
        super().__init__()
        self.name = "weather"
        self.category = "information"
        self.description = "Get current weather information for any location"
    
    def get_declarations(self) -> List[Dict[str, Any]]:
        """Return function declarations for weather operations."""
        return [
            {
                "name": "get_weather",
                "description": "Gets the current weather for a given location with temperature, conditions, and humidity.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "location": {
                            "type": "string",
                            "description": "The city and state/country, e.g. 'San Francisco, CA' or 'London, UK'"
                        },
                        "units": {
                            "type": "string",
                            "enum": ["celsius", "fahrenheit"],
                            "description": "Temperature units to use"
                        }
                    },
                    "required": ["location"]
                }
            },
            {
                "name": "get_forecast",
                "description": "Gets the weather forecast for the next few days for a given location.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "location": {
                            "type": "string",
                            "description": "The city and state/country, e.g. 'San Francisco, CA' or 'London, UK'"
                        },
                        "days": {
                            "type": "integer",
                            "description": "Number of days to forecast (1-7)",
                            "minimum": 1,
                            "maximum": 7
                        },
                        "units": {
                            "type": "string",
                            "enum": ["celsius", "fahrenheit"],
                            "description": "Temperature units to use"
                        }
                    },
                    "required": ["location"]
                }
            }
        ]
    
    def execute(self, function_name: str, **kwargs) -> Dict[str, Any]:
        """Execute weather functions."""
        try:
            if function_name == "get_weather":
                return self._get_weather(**kwargs)
            elif function_name == "get_forecast":
                return self._get_forecast(**kwargs)
            else:
                raise ValueError(f"Unknown function: {function_name}")
                
        except Exception as e:
            return self.handle_error(e, function_name)
    
    def _get_weather(self, location: str, units: str = "celsius") -> Dict[str, Any]:
        """Get current weather (mock implementation)."""
        # Mock weather data
        conditions = ["sunny", "cloudy", "rainy", "partly cloudy", "overcast", "foggy"]
        condition = random.choice(conditions)
        
        if units == "fahrenheit":
            temperature = random.randint(32, 95)
            unit_symbol = "°F"
        else:
            temperature = random.randint(0, 35)
            unit_symbol = "°C"
        
        humidity = random.randint(30, 90)
        wind_speed = random.randint(0, 25)
        
        weather_data = {
            "location": location,
            "temperature": temperature,
            "units": units,
            "condition": condition,
            "humidity": f"{humidity}%",
            "wind_speed": f"{wind_speed} mph",
            "description": f"It's {condition} in {location} with a temperature of {temperature}{unit_symbol}"
        }
        
        return self.format_success_response(weather_data, "get_weather")
    
    def _get_forecast(self, location: str, days: int = 3, units: str = "celsius") -> Dict[str, Any]:
        """Get weather forecast (mock implementation)."""
        days = min(max(days, 1), 7)  # Ensure days is between 1 and 7
        
        conditions = ["sunny", "cloudy", "rainy", "partly cloudy", "overcast"]
        forecast = []
        
        for day in range(days):
            condition = random.choice(conditions)
            
            if units == "fahrenheit":
                high_temp = random.randint(50, 95)
                low_temp = random.randint(32, high_temp - 10)
                unit_symbol = "°F"
            else:
                high_temp = random.randint(10, 35)
                low_temp = random.randint(-5, high_temp - 5)
                unit_symbol = "°C"
            
            day_forecast = {
                "day": f"Day {day + 1}",
                "condition": condition,
                "high_temperature": f"{high_temp}{unit_symbol}",
                "low_temperature": f"{low_temp}{unit_symbol}",
                "description": f"{condition.title()} with highs of {high_temp}{unit_symbol}"
            }
            forecast.append(day_forecast)
        
        forecast_data = {
            "location": location,
            "days": days,
            "units": units,
            "forecast": forecast
        }
        
        return self.format_success_response(forecast_data, "get_forecast")
    
    def get_python_functions(self) -> List[callable]:
        """Return Python functions for automatic function calling."""
        
        def get_weather(location: str, units: str = "celsius") -> Dict[str, Any]:
            """Gets the current weather for a given location.
            
            Args:
                location: The city and state/country, e.g. 'San Francisco, CA'
                units: Temperature units ('celsius' or 'fahrenheit')
                
            Returns:
                Dictionary containing weather information
            """
            result = self._get_weather(location, units)
            return result.get('result', {})
        
        def get_forecast(location: str, days: int = 3, units: str = "celsius") -> Dict[str, Any]:
            """Gets the weather forecast for the next few days.
            
            Args:
                location: The city and state/country, e.g. 'San Francisco, CA'
                days: Number of days to forecast (1-7)
                units: Temperature units ('celsius' or 'fahrenheit')
                
            Returns:
                Dictionary containing forecast information
            """
            result = self._get_forecast(location, days, units)
            return result.get('result', {})
        
        return [get_weather, get_forecast]
