#!/usr/bin/env python3
"""
Demonstration of the File Reader Tool
Shows how the AI assistant can read and analyze files
"""

from tools.file_reader_tool import <PERSON><PERSON>eaderTool

def demo_file_reader():
    """Demonstrate the file reader tool capabilities"""
    print("🔍 File Reader Tool Demonstration")
    print("=" * 50)
    
    # Create file reader tool instance
    file_reader = FileReaderTool()
    
    print(f"📦 Tool: {file_reader.name}")
    print(f"📝 Description: {file_reader.description}")
    print(f"🏷️  Category: {file_reader.category}")
    
    # Show available functions
    declarations = file_reader.get_declarations()
    print(f"\n🔧 Available Functions ({len(declarations)}):")
    for decl in declarations:
        print(f"  • {decl['name']}: {decl['description']}")
    
    print("\n" + "=" * 50)
    print("🧪 TESTING FILE READER FUNCTIONS")
    print("=" * 50)
    
    # Test 1: Read a single file
    print("\n1️⃣ Reading function_calling.txt (first 20 lines)")
    print("-" * 40)
    try:
        result = file_reader.execute("read_file", 
                                   file_path="function_calling.txt", 
                                   max_lines=20)
        if result['success']:
            data = result['result']
            print(f"✅ File read successfully!")
            print(f"   📄 File: {data['file_path']}")
            print(f"   📏 Lines read: {data['line_count']}")
            print(f"   📊 File size: {data['file_size_formatted']}")
            print(f"   🔤 Characters: {data['character_count']}")
            print(f"   📝 Content preview:")
            content_lines = data['content'].split('\n')[:5]
            for i, line in enumerate(content_lines, 1):
                print(f"      {i}: {line[:80]}{'...' if len(line) > 80 else ''}")
        else:
            print(f"❌ Error: {result['error']}")
    except Exception as e:
        print(f"❌ Exception: {e}")
    
    # Test 2: Read multiple files
    print("\n2️⃣ Reading multiple files (text_generation.txt and streaming.txt)")
    print("-" * 40)
    try:
        result = file_reader.execute("read_multiple_files",
                                   file_paths=["text_generation.txt", "streaming.txt"],
                                   max_lines_per_file=10,
                                   include_metadata=True)
        if result['success']:
            data = result['result']
            print(f"✅ Multiple files read successfully!")
            print(f"   📁 Files requested: {data['files_requested']}")
            print(f"   ✅ Files read: {data['files_read']}")
            print(f"   📊 Total lines: {data['total_lines']}")
            print(f"   💾 Total size: {data['total_size_formatted']}")
            print(f"   📈 Success rate: {data['success_rate']:.1%}")
            
            for file_data in data['files']:
                print(f"     • {file_data['file_path']}: {file_data['line_count']} lines")
        else:
            print(f"❌ Error: {result['error']}")
    except Exception as e:
        print(f"❌ Exception: {e}")
    
    # Test 3: Analyze file content
    print("\n3️⃣ Analyzing ai_assistant.py (code analysis)")
    print("-" * 40)
    try:
        result = file_reader.execute("analyze_file_content",
                                   file_path="ai_assistant.py",
                                   analysis_type="code_analysis")
        if result['success']:
            data = result['result']
            print(f"✅ File analysis completed!")
            print(f"   📄 File: {data['file_path']}")
            print(f"   🏷️  Content type: {data['content_type']}")
            
            basic = data['basic_stats']
            print(f"   📊 Basic stats:")
            print(f"     • Lines: {basic['line_count']}")
            print(f"     • Words: {basic['word_count']}")
            print(f"     • Characters: {basic['character_count']}")
            print(f"     • File size: {basic['file_size']}")
            
            if 'detailed_stats' in data:
                detailed = data['detailed_stats']
                print(f"   📈 Detailed stats:")
                print(f"     • Non-empty lines: {detailed['non_empty_lines']}")
                print(f"     • Empty lines: {detailed['empty_lines']}")
                print(f"     • Avg line length: {detailed['average_line_length']:.1f}")
            
            if 'code_stats' in data:
                code = data['code_stats']
                print(f"   💻 Code stats:")
                print(f"     • Comment lines: {code['comment_lines']}")
                print(f"     • Import lines: {code['import_lines']}")
                print(f"     • Function definitions: {code['function_definitions']}")
                print(f"     • Class definitions: {code['class_definitions']}")
        else:
            print(f"❌ Error: {result['error']}")
    except Exception as e:
        print(f"❌ Exception: {e}")
    
    # Test 4: Search in files
    print("\n4️⃣ Searching for 'function' in documentation files")
    print("-" * 40)
    try:
        result = file_reader.execute("search_in_files",
                                   file_paths=["function_calling.txt", "text_generation.txt"],
                                   search_term="function",
                                   case_sensitive=False,
                                   max_results=5)
        if result['success']:
            data = result['result']
            print(f"✅ Search completed!")
            print(f"   🔍 Search term: '{data['search_term']}'")
            print(f"   📁 Files searched: {data['files_searched']}")
            print(f"   📄 Files with matches: {data['files_with_matches']}")
            print(f"   🎯 Total matches: {data['total_matches']}")
            
            for file_result in data['results'][:2]:  # Show first 2 files
                print(f"   📄 {file_result['file_path']}:")
                for match in file_result['matches'][:3]:  # Show first 3 matches
                    line_preview = match['line_content'][:60] + "..." if len(match['line_content']) > 60 else match['line_content']
                    print(f"     Line {match['line_number']}: {line_preview}")
        else:
            print(f"❌ Error: {result['error']}")
    except Exception as e:
        print(f"❌ Exception: {e}")
    
    print("\n" + "=" * 50)
    print("✅ File Reader Tool demonstration completed!")
    print("🚀 The tool is ready for use with the AI assistant!")

if __name__ == "__main__":
    demo_file_reader()
