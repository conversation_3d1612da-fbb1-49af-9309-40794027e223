{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://blockchainnodeengine.googleapis.com/", "batchPath": "batch", "canonicalName": "Blockchain Node Engine", "description": "", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/blockchain-node-engine", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "blockchainnodeengine:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://blockchainnodeengine.mtls.googleapis.com/", "name": "blockchainnodeengine", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"resources": {"locations": {"methods": {"get": {"description": "Gets information about a location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}", "httpMethod": "GET", "id": "blockchainnodeengine.projects.locations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Resource name for the location.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Location"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists information about the supported locations for this service.", "flatPath": "v1/projects/{projectsId}/locations", "httpMethod": "GET", "id": "blockchainnodeengine.projects.locations.list", "parameterOrder": ["name"], "parameters": {"extraLocationTypes": {"description": "Optional. A list of extra location types that should be used as conditions for controlling the visibility of the locations.", "location": "query", "repeated": true, "type": "string"}, "filter": {"description": "A filter to narrow down results to a preferred subset. The filtering language accepts strings like `\"displayName=tokyo\"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).", "location": "query", "type": "string"}, "name": {"description": "The resource that owns the locations collection, if applicable.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The maximum number of results to return. If not set, the service selects a default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.", "location": "query", "type": "string"}}, "path": "v1/{+name}/locations", "response": {"$ref": "ListLocationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"blockchainNodes": {"methods": {"create": {"description": "Creates a new blockchain node in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/blockchainNodes", "httpMethod": "POST", "id": "blockchainnodeengine.projects.locations.blockchainNodes.create", "parameterOrder": ["parent"], "parameters": {"blockchainNodeId": {"description": "Required. ID of the requesting object.", "location": "query", "type": "string"}, "parent": {"description": "Required. Value for parent.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+parent}/blockchainNodes", "request": {"$ref": "BlockchainNode"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single blockchain node.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/blockchainNodes/{blockchainNodesId}", "httpMethod": "DELETE", "id": "blockchainnodeengine.projects.locations.blockchainNodes.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully qualified name of the blockchain node to delete. e.g. `projects/my-project/locations/us-central1/blockchainNodes/my-node`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/blockchainNodes/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single blockchain node.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/blockchainNodes/{blockchainNodesId}", "httpMethod": "GET", "id": "blockchainnodeengine.projects.locations.blockchainNodes.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully qualified name of the blockchain node to fetch. e.g. `projects/my-project/locations/us-central1/blockchainNodes/my-node`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/blockchainNodes/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "BlockchainNode"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists blockchain nodes in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/blockchainNodes", "httpMethod": "GET", "id": "blockchainnodeengine.projects.locations.blockchainNodes.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Filtering results.", "location": "query", "type": "string"}, "orderBy": {"description": "Hint for how to order the results.", "location": "query", "type": "string"}, "pageSize": {"description": "Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return.", "location": "query", "type": "string"}, "parent": {"description": "Required. Parent value for `ListNodesRequest`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/blockchainNodes", "response": {"$ref": "ListBlockchainNodesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single blockchain node.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/blockchainNodes/{blockchainNodesId}", "httpMethod": "PATCH", "id": "blockchainnodeengine.projects.locations.blockchainNodes.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. The fully qualified name of the blockchain node. e.g. `projects/my-project/locations/us-central1/blockchainNodes/my-node`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/blockchainNodes/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten in the Blockchain node resource by the update. The fields specified in the `update_mask` are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "BlockchainNode"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "operations": {"methods": {"cancel": {"description": "Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel", "httpMethod": "POST", "id": "blockchainnodeengine.projects.locations.operations.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be cancelled.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:cancel", "request": {"$ref": "CancelOperationRequest"}, "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "DELETE", "id": "blockchainnodeengine.projects.locations.operations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be deleted.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "GET", "id": "blockchainnodeengine.projects.locations.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations", "httpMethod": "GET", "id": "blockchainnodeengine.projects.locations.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1/{+name}/operations", "response": {"$ref": "ListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}, "revision": "20250416", "rootUrl": "https://blockchainnodeengine.googleapis.com/", "schemas": {"BlockchainNode": {"description": "A representation of a blockchain node.", "id": "BlockchainNode", "properties": {"blockchainType": {"description": "Immutable. The blockchain type of the node.", "enum": ["BLOCKCHAIN_TYPE_UNSPECIFIED", "ETHEREUM"], "enumDescriptions": ["Blockchain type has not been specified, but should be.", "The blockchain type is Ethereum."], "type": "string"}, "connectionInfo": {"$ref": "ConnectionInfo", "description": "Output only. The connection information used to interact with a blockchain node.", "readOnly": true}, "createTime": {"description": "Output only. The timestamp at which the blockchain node was first created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "ethereumDetails": {"$ref": "EthereumDetails", "description": "Ethereum-specific blockchain node details."}, "labels": {"additionalProperties": {"type": "string"}, "description": "User-provided key-value pairs.", "type": "object"}, "name": {"description": "Output only. The fully qualified name of the blockchain node. e.g. `projects/my-project/locations/us-central1/blockchainNodes/my-node`.", "readOnly": true, "type": "string"}, "privateServiceConnectEnabled": {"deprecated": true, "description": "Optional. When true, the node is only accessible via Private Service Connect; no public endpoints are exposed. Otherwise, the node is only accessible via public endpoints. Warning: These nodes are deprecated, please use public endpoints instead.", "type": "boolean"}, "state": {"description": "Output only. A status representing the state of the node.", "enum": ["STATE_UNSPECIFIED", "CREATING", "DELETING", "RUNNING", "ERROR", "UPDATING", "REPAIRING", "RECONCILING", "SYNCING"], "enumDescriptions": ["The state has not been specified.", "The node has been requested and is in the process of being created.", "The existing node is undergoing deletion, but is not yet finished.", "The node is running and ready for use.", "The node is in an unexpected or errored state.", "The node is currently being updated.", "The node is currently being repaired.", "The node is currently being reconciled.", "The node is syncing, which is the process by which it obtains the latest block and current global state."], "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The timestamp at which the blockchain node was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "CancelOperationRequest": {"description": "The request message for Operations.CancelOperation.", "id": "CancelOperationRequest", "properties": {}, "type": "object"}, "ConnectionInfo": {"description": "The connection information through which to interact with a blockchain node.", "id": "ConnectionInfo", "properties": {"endpointInfo": {"$ref": "EndpointInfo", "description": "Output only. The endpoint information through which to interact with a blockchain node.", "readOnly": true}, "serviceAttachment": {"description": "Output only. A service attachment that exposes a node, and has the following format: projects/{project}/regions/{region}/serviceAttachments/{service_attachment_name}", "readOnly": true, "type": "string"}}, "type": "object"}, "EndpointInfo": {"description": "Contains endpoint information through which to interact with a blockchain node.", "id": "EndpointInfo", "properties": {"jsonRpcApiEndpoint": {"description": "Output only. The assigned URL for the node JSON-RPC API endpoint.", "readOnly": true, "type": "string"}, "websocketsApiEndpoint": {"description": "Output only. The assigned URL for the node WebSockets API endpoint.", "readOnly": true, "type": "string"}}, "type": "object"}, "EthereumDetails": {"description": "Ethereum-specific blockchain node details.", "id": "EthereumDetails", "properties": {"additionalEndpoints": {"$ref": "EthereumEndpoints", "description": "Output only. Ethereum-specific endpoint information.", "readOnly": true}, "apiEnableAdmin": {"description": "Immutable. Enables JSON-RPC access to functions in the `admin` namespace. Defaults to `false`.", "type": "boolean"}, "apiEnableDebug": {"description": "Immutable. Enables JSON-RPC access to functions in the `debug` namespace. Defaults to `false`.", "type": "boolean"}, "consensusClient": {"description": "Immutable. The consensus client.", "enum": ["CONSENSUS_CLIENT_UNSPECIFIED", "LIGHTHOUSE", "ERIGON_EMBEDDED_CONSENSUS_LAYER"], "enumDeprecated": [false, false, true], "enumDescriptions": ["Consensus client has not been specified, but should be.", "Consensus client implementation written in Rust, maintained by Sigma Prime. See [Lighthouse - Sigma Prime](https://lighthouse.sigmaprime.io/) for details.", "Erigon's embedded consensus client embedded in the execution client. Note this option is not currently available when creating new blockchain nodes. See [Erigon on GitHub](https://github.com/ledgerwatch/erigon#embedded-consensus-layer) for details."], "type": "string"}, "executionClient": {"description": "Immutable. The execution client", "enum": ["EXECUTION_CLIENT_UNSPECIFIED", "GETH", "ERIGON"], "enumDescriptions": ["Execution client has not been specified, but should be.", "Official Go implementation of the Ethereum protocol. See [go-ethereum](https://geth.ethereum.org/) for details.", "An implementation of Ethereum (execution client), on the efficiency frontier, written in Go. See [<PERSON><PERSON><PERSON> on GitHub](https://github.com/ledgerwatch/erigon) for details."], "type": "string"}, "gethDetails": {"$ref": "GethDetails", "description": "Details for the Geth execution client."}, "network": {"description": "Immutable. The Ethereum environment being accessed.", "enum": ["NETWORK_UNSPECIFIED", "MAINNET", "TESTNET_GOERLI_PRATER", "TESTNET_SEPOLIA", "TESTNET_HOLESKY"], "enumDeprecated": [false, false, true, false, false], "enumDescriptions": ["The network has not been specified, but should be.", "The Ethereum Mainnet.", "Deprecated: The Ethereum Testnet based on Goerli protocol. Please use another test network.", "The Ethereum Testnet based on Sepolia/Bepolia protocol. See https://github.com/eth-clients/sepolia.", "The Ethereum Testnet based on <PERSON>sky specification. See https://github.com/eth-clients/holesky."], "type": "string"}, "nodeType": {"description": "Immutable. The type of Ethereum node.", "enum": ["NODE_TYPE_UNSPECIFIED", "LIGHT", "FULL", "ARCHIVE"], "enumDescriptions": ["Node type has not been specified, but should be.", "An Ethereum node that only downloads Ethereum block headers.", "Keeps a complete copy of the blockchain data, and contributes to the network by receiving, validating, and forwarding transactions.", "Holds the same data as full node as well as all of the blockchain's history state data dating back to the Genesis Block."], "type": "string"}, "validatorConfig": {"$ref": "ValidatorConfig", "description": "Configuration for validator-related parameters on the beacon client, and for any GCP-managed validator client."}}, "type": "object"}, "EthereumEndpoints": {"description": "Contains endpoint information specific to Ethereum nodes.", "id": "EthereumEndpoints", "properties": {"beaconApiEndpoint": {"description": "Output only. The assigned URL for the node's Beacon API endpoint.", "readOnly": true, "type": "string"}, "beaconPrometheusMetricsApiEndpoint": {"description": "Output only. The assigned URL for the node's Beacon Prometheus metrics endpoint. See [Prometheus Metrics](https://lighthouse-book.sigmaprime.io/advanced_metrics.html) for more details.", "readOnly": true, "type": "string"}, "executionClientPrometheusMetricsApiEndpoint": {"description": "Output only. The assigned URL for the node's execution client's Prometheus metrics endpoint.", "readOnly": true, "type": "string"}}, "type": "object"}, "GethDetails": {"description": "Options for the Geth execution client. See [Command-line Options](https://geth.ethereum.org/docs/fundamentals/command-line-options) for more details.", "id": "GethDetails", "properties": {"garbageCollectionMode": {"description": "Immutable. Blockchain garbage collection mode.", "enum": ["GARBAGE_COLLECTION_MODE_UNSPECIFIED", "FULL", "ARCHIVE"], "enumDescriptions": ["The garbage collection has not been specified.", "Configures <PERSON><PERSON>'s garbage collection so that older data not needed for a full node is deleted. This is the default mode when creating a full node.", "Configures Geth's garbage collection so that old data is never deleted. This is the default mode when creating an archive node. This value can also be chosen when creating a full node in order to create a partial/recent archive node. See [Sync modes](https://geth.ethereum.org/docs/fundamentals/sync-modes) for more details."], "type": "string"}}, "type": "object"}, "GoogleProtobufEmpty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "GoogleProtobufEmpty", "properties": {}, "type": "object"}, "ListBlockchainNodesResponse": {"description": "Message for response to listing blockchain nodes.", "id": "ListBlockchainNodesResponse", "properties": {"blockchainNodes": {"description": "The list of nodes", "items": {"$ref": "BlockchainNode"}, "type": "array"}, "nextPageToken": {"description": "A token identifying a page of results the server should return.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListLocationsResponse": {"description": "The response message for Locations.ListLocations.", "id": "ListLocationsResponse", "properties": {"locations": {"description": "A list of locations that matches the specified filter in the request.", "items": {"$ref": "Location"}, "type": "array"}, "nextPageToken": {"description": "The standard List next-page token.", "type": "string"}}, "type": "object"}, "ListOperationsResponse": {"description": "The response message for Operations.ListOperations.", "id": "ListOperationsResponse", "properties": {"nextPageToken": {"description": "The standard List next-page token.", "type": "string"}, "operations": {"description": "A list of operations that matches the specified filter in the request.", "items": {"$ref": "Operation"}, "type": "array"}}, "type": "object"}, "Location": {"description": "A resource that represents a Google Cloud location.", "id": "Location", "properties": {"displayName": {"description": "The friendly name for this location, typically a nearby city name. For example, \"Tokyo\".", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Cross-service attributes for the location. For example {\"cloud.googleapis.com/region\": \"us-east1\"}", "type": "object"}, "locationId": {"description": "The canonical id for this location. For example: `\"us-east1\"`.", "type": "string"}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata. For example the available capacity at the given location.", "type": "object"}, "name": {"description": "Resource name for the location, which may vary between implementations. For example: `\"projects/example-project/locations/us-east1\"`", "type": "string"}}, "type": "object"}, "Operation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "Operation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "Status", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "OperationMetadata": {"description": "Represents the metadata of the long-running operation.", "id": "OperationMetadata", "properties": {"apiVersion": {"description": "Output only. API version used to start the operation.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "requestedCancellation": {"description": "Output only. Identifies whether the user has requested cancellation of the operation. Operations that have been cancelled successfully have `Operation.error` value with a `google.rpc.Status.code` of `1`, corresponding to `Code.CANCELLED`.", "readOnly": true, "type": "boolean"}, "statusMessage": {"description": "Output only. Human-readable status of the operation, if any.", "readOnly": true, "type": "string"}, "target": {"description": "Output only. Server-defined resource path for the target of the operation.", "readOnly": true, "type": "string"}, "verb": {"description": "Output only. Name of the verb executed by the operation.", "readOnly": true, "type": "string"}}, "type": "object"}, "Status": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "Status", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "ValidatorConfig": {"description": "Configuration for validator-related parameters on the beacon client, and for any GCP-managed validator client.", "id": "ValidatorConfig", "properties": {"beaconFeeRecipient": {"description": "An Ethereum address which the beacon client will send fee rewards to if no recipient is configured in the validator client. See https://lighthouse-book.sigmaprime.io/suggested-fee-recipient.html or https://docs.prylabs.network/docs/execution-node/fee-recipient for examples of how this is used. Note that while this is often described as \"suggested\", as we run the execution node we can trust the execution node, and therefore this is considered enforced.", "type": "string"}, "managedValidatorClient": {"deprecated": true, "description": "Immutable. When true, deploys a GCP-managed validator client alongside the beacon client.", "type": "boolean"}, "mevRelayUrls": {"description": "URLs for MEV-relay services to use for block building. When set, a GCP-managed MEV-boost service is configured on the beacon client.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}}, "servicePath": "", "title": "Blockchain Node Engine API", "version": "v1", "version_module": true}