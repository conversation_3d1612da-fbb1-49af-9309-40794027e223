{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://eventarc.googleapis.com/", "batchPath": "batch", "canonicalName": "Eventarc", "description": "Build event-driven applications on Google Cloud Platform.", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/eventarc", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "eventarc:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://eventarc.mtls.googleapis.com/", "name": "eventarc", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"resources": {"locations": {"methods": {"get": {"description": "Gets information about a location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}", "httpMethod": "GET", "id": "eventarc.projects.locations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Resource name for the location.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Location"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getGoogleChannelConfig": {"description": "Get a GoogleChannelConfig. The name of the GoogleChannelConfig in the response is ALWAYS coded with projectID.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/googleChannelConfig", "httpMethod": "GET", "id": "eventarc.projects.locations.getGoogleChannelConfig", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the config to get.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/googleChannelConfig$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleChannelConfig"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists information about the supported locations for this service.", "flatPath": "v1/projects/{projectsId}/locations", "httpMethod": "GET", "id": "eventarc.projects.locations.list", "parameterOrder": ["name"], "parameters": {"extraLocationTypes": {"description": "Optional. A list of extra location types that should be used as conditions for controlling the visibility of the locations.", "location": "query", "repeated": true, "type": "string"}, "filter": {"description": "A filter to narrow down results to a preferred subset. The filtering language accepts strings like `\"displayName=tokyo\"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).", "location": "query", "type": "string"}, "name": {"description": "The resource that owns the locations collection, if applicable.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The maximum number of results to return. If not set, the service selects a default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.", "location": "query", "type": "string"}}, "path": "v1/{+name}/locations", "response": {"$ref": "ListLocationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "updateGoogleChannelConfig": {"description": "Update a single GoogleChannelConfig", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/googleChannelConfig", "httpMethod": "PATCH", "id": "eventarc.projects.locations.updateGoogleChannelConfig", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the config. Must be in the format of, `projects/{project}/locations/{location}/googleChannelConfig`. In API responses, the config name always includes the projectID, regardless of whether the projectID or projectNumber was provided.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/googleChannelConfig$", "required": true, "type": "string"}, "updateMask": {"description": "The fields to be updated; only fields explicitly provided are updated. If no field mask is provided, all provided fields in the request are updated. To update all fields, provide a field mask of \"*\".", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "GoogleChannelConfig"}, "response": {"$ref": "GoogleChannelConfig"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"channelConnections": {"methods": {"create": {"description": "Create a new ChannelConnection in a particular project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/channelConnections", "httpMethod": "POST", "id": "eventarc.projects.locations.channelConnections.create", "parameterOrder": ["parent"], "parameters": {"channelConnectionId": {"description": "Required. The user-provided ID to be assigned to the channel connection.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent collection in which to add this channel connection.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/channelConnections", "request": {"$ref": "ChannelConnection"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Delete a single ChannelConnection.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/channelConnections/{channelConnectionsId}", "httpMethod": "DELETE", "id": "eventarc.projects.locations.channelConnections.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the channel connection to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/channelConnections/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Get a single ChannelConnection.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/channelConnections/{channelConnectionsId}", "httpMethod": "GET", "id": "eventarc.projects.locations.channelConnections.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the channel connection to get.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/channelConnections/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "ChannelConnection"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/channelConnections/{channelConnectionsId}:getIamPolicy", "httpMethod": "GET", "id": "eventarc.projects.locations.channelConnections.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/channelConnections/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "List channel connections.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/channelConnections", "httpMethod": "GET", "id": "eventarc.projects.locations.channelConnections.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of channel connections to return on each page. Note: The service may send fewer responses.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The page token; provide the value from the `next_page_token` field in a previous `ListChannelConnections` call to retrieve the subsequent page. When paginating, all other parameters provided to `ListChannelConnetions` match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent collection from which to list channel connections.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/channelConnections", "response": {"$ref": "ListChannelConnectionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/channelConnections/{channelConnectionsId}:setIamPolicy", "httpMethod": "POST", "id": "eventarc.projects.locations.channelConnections.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/channelConnections/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/channelConnections/{channelConnectionsId}:testIamPermissions", "httpMethod": "POST", "id": "eventarc.projects.locations.channelConnections.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/channelConnections/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "channels": {"methods": {"create": {"description": "Create a new channel in a particular project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/channels", "httpMethod": "POST", "id": "eventarc.projects.locations.channels.create", "parameterOrder": ["parent"], "parameters": {"channelId": {"description": "Required. The user-provided ID to be assigned to the channel.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent collection in which to add this channel.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "validateOnly": {"description": "Optional. If set, validate the request and preview the review, but do not post it.", "location": "query", "type": "boolean"}}, "path": "v1/{+parent}/channels", "request": {"$ref": "Channel"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Delete a single channel.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/channels/{channelsId}", "httpMethod": "DELETE", "id": "eventarc.projects.locations.channels.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the channel to be deleted.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/channels/[^/]+$", "required": true, "type": "string"}, "validateOnly": {"description": "Optional. If set, validate the request and preview the review, but do not post it.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Get a single Channel.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/channels/{channelsId}", "httpMethod": "GET", "id": "eventarc.projects.locations.channels.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the channel to get.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/channels/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Channel"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/channels/{channelsId}:getIamPolicy", "httpMethod": "GET", "id": "eventarc.projects.locations.channels.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/channels/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "List channels.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/channels", "httpMethod": "GET", "id": "eventarc.projects.locations.channels.list", "parameterOrder": ["parent"], "parameters": {"orderBy": {"description": "The sorting order of the resources returned. Value should be a comma-separated list of fields. The default sorting order is ascending. To specify descending order for a field, append a `desc` suffix; for example: `name desc, channel_id`.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of channels to return on each page. Note: The service may send fewer.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The page token; provide the value from the `next_page_token` field in a previous `ListChannels` call to retrieve the subsequent page. When paginating, all other parameters provided to `ListChannels` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent collection to list channels on.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/channels", "response": {"$ref": "ListChannelsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Update a single channel.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/channels/{channelsId}", "httpMethod": "PATCH", "id": "eventarc.projects.locations.channels.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the channel. Must be unique within the location on the project and must be in `projects/{project}/locations/{location}/channels/{channel_id}` format.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/channels/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "The fields to be updated; only fields explicitly provided are updated. If no field mask is provided, all provided fields in the request are updated. To update all fields, provide a field mask of \"*\".", "format": "google-fieldmask", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set, validate the request and preview the review, but do not post it.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}", "request": {"$ref": "Channel"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/channels/{channelsId}:setIamPolicy", "httpMethod": "POST", "id": "eventarc.projects.locations.channels.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/channels/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/channels/{channelsId}:testIamPermissions", "httpMethod": "POST", "id": "eventarc.projects.locations.channels.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/channels/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "enrollments": {"methods": {"create": {"description": "Create a new Enrollment in a particular project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/enrollments", "httpMethod": "POST", "id": "eventarc.projects.locations.enrollments.create", "parameterOrder": ["parent"], "parameters": {"enrollmentId": {"description": "Required. The user-provided ID to be assigned to the Enrollment. It should match the format `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent collection in which to add this enrollment.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "validateOnly": {"description": "Optional. If set, validate the request and preview the review, but do not post it.", "location": "query", "type": "boolean"}}, "path": "v1/{+parent}/enrollments", "request": {"$ref": "Enrollment"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Delete a single Enrollment.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/enrollments/{enrollmentsId}", "httpMethod": "DELETE", "id": "eventarc.projects.locations.enrollments.delete", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "Optional. If set to true, and the Enrollment is not found, the request will succeed but no action will be taken on the server.", "location": "query", "type": "boolean"}, "etag": {"description": "Optional. If provided, the Enrollment will only be deleted if the etag matches the current etag on the resource.", "location": "query", "type": "string"}, "name": {"description": "Required. The name of the Enrollment to be deleted.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/enrollments/[^/]+$", "required": true, "type": "string"}, "validateOnly": {"description": "Optional. If set, validate the request and preview the review, but do not post it.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Get a single Enrollment.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/enrollments/{enrollmentsId}", "httpMethod": "GET", "id": "eventarc.projects.locations.enrollments.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the Enrollment to get.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/enrollments/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Enrollment"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/enrollments/{enrollmentsId}:getIamPolicy", "httpMethod": "GET", "id": "eventarc.projects.locations.enrollments.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/enrollments/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "List Enrollments.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/enrollments", "httpMethod": "GET", "id": "eventarc.projects.locations.enrollments.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. The filter field that the list request will filter on. Possible filtersare described in https://google.aip.dev/160.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. The sorting order of the resources returned. Value should be a comma-separated list of fields. The default sorting order is ascending. To specify descending order for a field, append a `desc` suffix; for example: `name desc, update_time`.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of results to return on each page. Note: The service may send fewer.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The page token; provide the value from the `next_page_token` field in a previous call to retrieve the subsequent page. When paginating, all other parameters provided must match the previous call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent collection to list triggers on.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/enrollments", "response": {"$ref": "ListEnrollmentsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Update a single Enrollment.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/enrollments/{enrollmentsId}", "httpMethod": "PATCH", "id": "eventarc.projects.locations.enrollments.patch", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "Optional. If set to true, and the Enrollment is not found, a new Enrollment will be created. In this situation, `update_mask` is ignored.", "location": "query", "type": "boolean"}, "name": {"description": "Identifier. Resource name of the form projects/{project}/locations/{location}/enrollments/{enrollment}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/enrollments/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. The fields to be updated; only fields explicitly provided are updated. If no field mask is provided, all provided fields in the request are updated. To update all fields, provide a field mask of \"*\".", "format": "google-fieldmask", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set, validate the request and preview the review, but do not post it.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}", "request": {"$ref": "Enrollment"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/enrollments/{enrollmentsId}:setIamPolicy", "httpMethod": "POST", "id": "eventarc.projects.locations.enrollments.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/enrollments/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/enrollments/{enrollmentsId}:testIamPermissions", "httpMethod": "POST", "id": "eventarc.projects.locations.enrollments.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/enrollments/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "googleApiSources": {"methods": {"create": {"description": "Create a new GoogleApiSource in a particular project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/googleApiSources", "httpMethod": "POST", "id": "eventarc.projects.locations.googleApiSources.create", "parameterOrder": ["parent"], "parameters": {"googleApiSourceId": {"description": "Required. The user-provided ID to be assigned to the GoogleApiSource. It should match the format `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent collection in which to add this google api source.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "validateOnly": {"description": "Optional. If set, validate the request and preview the review, but do not post it.", "location": "query", "type": "boolean"}}, "path": "v1/{+parent}/googleApiSources", "request": {"$ref": "GoogleApiSource"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Delete a single GoogleApiSource.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/googleApiSources/{googleApiSourcesId}", "httpMethod": "DELETE", "id": "eventarc.projects.locations.googleApiSources.delete", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "Optional. If set to true, and the MessageBus is not found, the request will succeed but no action will be taken on the server.", "location": "query", "type": "boolean"}, "etag": {"description": "Optional. If provided, the MessageBus will only be deleted if the etag matches the current etag on the resource.", "location": "query", "type": "string"}, "name": {"description": "Required. The name of the GoogleApiSource to be deleted.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/googleApiSources/[^/]+$", "required": true, "type": "string"}, "validateOnly": {"description": "Optional. If set, validate the request and preview the review, but do not post it.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Get a single GoogleApiSource.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/googleApiSources/{googleApiSourcesId}", "httpMethod": "GET", "id": "eventarc.projects.locations.googleApiSources.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the google api source to get.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/googleApiSources/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleApiSource"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/googleApiSources/{googleApiSourcesId}:getIamPolicy", "httpMethod": "GET", "id": "eventarc.projects.locations.googleApiSources.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/googleApiSources/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "List GoogleApiSources.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/googleApiSources", "httpMethod": "GET", "id": "eventarc.projects.locations.googleApiSources.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. The filter field that the list request will filter on. Possible filtersare described in https://google.aip.dev/160.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. The sorting order of the resources returned. Value should be a comma-separated list of fields. The default sorting order is ascending. To specify descending order for a field, append a `desc` suffix; for example: `name desc, update_time`.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of results to return on each page. Note: The service may send fewer.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The page token; provide the value from the `next_page_token` field in a previous call to retrieve the subsequent page. When paginating, all other parameters provided must match the previous call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent collection to list GoogleApiSources on.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/googleApiSources", "response": {"$ref": "ListGoogleApiSourcesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Update a single GoogleApiSource.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/googleApiSources/{googleApiSourcesId}", "httpMethod": "PATCH", "id": "eventarc.projects.locations.googleApiSources.patch", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "Optional. If set to true, and the GoogleApiSource is not found, a new GoogleApiSource will be created. In this situation, `update_mask` is ignored.", "location": "query", "type": "boolean"}, "name": {"description": "Identifier. Resource name of the form projects/{project}/locations/{location}/googleApiSources/{google_api_source}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/googleApiSources/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. The fields to be updated; only fields explicitly provided are updated. If no field mask is provided, all provided fields in the request are updated. To update all fields, provide a field mask of \"*\".", "format": "google-fieldmask", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set, validate the request and preview the review, but do not post it.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}", "request": {"$ref": "GoogleApiSource"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/googleApiSources/{googleApiSourcesId}:setIamPolicy", "httpMethod": "POST", "id": "eventarc.projects.locations.googleApiSources.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/googleApiSources/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/googleApiSources/{googleApiSourcesId}:testIamPermissions", "httpMethod": "POST", "id": "eventarc.projects.locations.googleApiSources.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/googleApiSources/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "kafkaSources": {"methods": {"getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/kafkaSources/{kafkaSourcesId}:getIamPolicy", "httpMethod": "GET", "id": "eventarc.projects.locations.kafkaSources.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/kafkaSources/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/kafkaSources/{kafkaSourcesId}:setIamPolicy", "httpMethod": "POST", "id": "eventarc.projects.locations.kafkaSources.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/kafkaSources/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/kafkaSources/{kafkaSourcesId}:testIamPermissions", "httpMethod": "POST", "id": "eventarc.projects.locations.kafkaSources.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/kafkaSources/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "messageBuses": {"methods": {"create": {"description": "Create a new MessageBus in a particular project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/messageBuses", "httpMethod": "POST", "id": "eventarc.projects.locations.messageBuses.create", "parameterOrder": ["parent"], "parameters": {"messageBusId": {"description": "Required. The user-provided ID to be assigned to the MessageBus. It should match the format `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent collection in which to add this message bus.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "validateOnly": {"description": "Optional. If set, validate the request and preview the review, but do not post it.", "location": "query", "type": "boolean"}}, "path": "v1/{+parent}/messageBuses", "request": {"$ref": "MessageBus"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Delete a single message bus.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/messageBuses/{messageBusesId}", "httpMethod": "DELETE", "id": "eventarc.projects.locations.messageBuses.delete", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "Optional. If set to true, and the MessageBus is not found, the request will succeed but no action will be taken on the server.", "location": "query", "type": "boolean"}, "etag": {"description": "Optional. If provided, the MessageBus will only be deleted if the etag matches the current etag on the resource.", "location": "query", "type": "string"}, "name": {"description": "Required. The name of the MessageBus to be deleted.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/messageBuses/[^/]+$", "required": true, "type": "string"}, "validateOnly": {"description": "Optional. If set, validate the request and preview the review, but do not post it.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Get a single MessageBus.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/messageBuses/{messageBusesId}", "httpMethod": "GET", "id": "eventarc.projects.locations.messageBuses.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the message bus to get.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/messageBuses/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "MessageBus"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/messageBuses/{messageBusesId}:getIamPolicy", "httpMethod": "GET", "id": "eventarc.projects.locations.messageBuses.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/messageBuses/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "List message buses.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/messageBuses", "httpMethod": "GET", "id": "eventarc.projects.locations.messageBuses.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. The filter field that the list request will filter on. Possible filtersare described in https://google.aip.dev/160.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. The sorting order of the resources returned. Value should be a comma-separated list of fields. The default sorting order is ascending. To specify descending order for a field, append a `desc` suffix; for example: `name desc, update_time`.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of results to return on each page. Note: The service may send fewer.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The page token; provide the value from the `next_page_token` field in a previous call to retrieve the subsequent page. When paginating, all other parameters provided must match the previous call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent collection to list message buses on.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/messageBuses", "response": {"$ref": "ListMessageBusesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "listEnrollments": {"description": "List message bus enrollments.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/messageBuses/{messageBusesId}:listEnrollments", "httpMethod": "GET", "id": "eventarc.projects.locations.messageBuses.listEnrollments", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of results to return on each page. Note: The service may send fewer.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The page token; provide the value from the `next_page_token` field in a previous call to retrieve the subsequent page. When paginating, all other parameters provided must match the previous call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent message bus to list enrollments on.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/messageBuses/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}:listEnrollments", "response": {"$ref": "ListMessageBusEnrollmentsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Update a single message bus.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/messageBuses/{messageBusesId}", "httpMethod": "PATCH", "id": "eventarc.projects.locations.messageBuses.patch", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "Optional. If set to true, and the MessageBus is not found, a new MessageBus will be created. In this situation, `update_mask` is ignored.", "location": "query", "type": "boolean"}, "name": {"description": "Identifier. Resource name of the form projects/{project}/locations/{location}/messageBuses/{message_bus}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/messageBuses/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. The fields to be updated; only fields explicitly provided are updated. If no field mask is provided, all provided fields in the request are updated. To update all fields, provide a field mask of \"*\".", "format": "google-fieldmask", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set, validate the request and preview the review, but do not post it.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}", "request": {"$ref": "MessageBus"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/messageBuses/{messageBusesId}:setIamPolicy", "httpMethod": "POST", "id": "eventarc.projects.locations.messageBuses.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/messageBuses/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/messageBuses/{messageBusesId}:testIamPermissions", "httpMethod": "POST", "id": "eventarc.projects.locations.messageBuses.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/messageBuses/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "operations": {"methods": {"cancel": {"description": "Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel", "httpMethod": "POST", "id": "eventarc.projects.locations.operations.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be cancelled.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:cancel", "request": {"$ref": "GoogleLongrunningCancelOperationRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "DELETE", "id": "eventarc.projects.locations.operations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be deleted.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "GET", "id": "eventarc.projects.locations.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations", "httpMethod": "GET", "id": "eventarc.projects.locations.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1/{+name}/operations", "response": {"$ref": "GoogleLongrunningListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "pipelines": {"methods": {"create": {"description": "Create a new Pipeline in a particular project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/pipelines", "httpMethod": "POST", "id": "eventarc.projects.locations.pipelines.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent collection in which to add this pipeline.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "pipelineId": {"description": "Required. The user-provided ID to be assigned to the Pipeline. It should match the format `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set, validate the request and preview the review, but do not post it.", "location": "query", "type": "boolean"}}, "path": "v1/{+parent}/pipelines", "request": {"$ref": "Pipeline"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Delete a single pipeline.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/pipelines/{pipelinesId}", "httpMethod": "DELETE", "id": "eventarc.projects.locations.pipelines.delete", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "Optional. If set to true, and the Pipeline is not found, the request will succeed but no action will be taken on the server.", "location": "query", "type": "boolean"}, "etag": {"description": "Optional. If provided, the Pipeline will only be deleted if the etag matches the current etag on the resource.", "location": "query", "type": "string"}, "name": {"description": "Required. The name of the Pipeline to be deleted.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/pipelines/[^/]+$", "required": true, "type": "string"}, "validateOnly": {"description": "Optional. If set, validate the request and preview the review, but do not post it.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Get a single Pipeline.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/pipelines/{pipelinesId}", "httpMethod": "GET", "id": "eventarc.projects.locations.pipelines.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the pipeline to get.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/pipelines/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Pipeline"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/pipelines/{pipelinesId}:getIamPolicy", "httpMethod": "GET", "id": "eventarc.projects.locations.pipelines.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/pipelines/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "List pipelines.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/pipelines", "httpMethod": "GET", "id": "eventarc.projects.locations.pipelines.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. The filter field that the list request will filter on. Possible filters are described in https://google.aip.dev/160.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. The sorting order of the resources returned. Value should be a comma-separated list of fields. The default sorting order is ascending. To specify descending order for a field, append a `desc` suffix; for example: `name desc, update_time`.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of results to return on each page. Note: The service may send fewer.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The page token; provide the value from the `next_page_token` field in a previous call to retrieve the subsequent page. When paginating, all other parameters provided must match the previous call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent collection to list pipelines on.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/pipelines", "response": {"$ref": "ListPipelinesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Update a single pipeline.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/pipelines/{pipelinesId}", "httpMethod": "PATCH", "id": "eventarc.projects.locations.pipelines.patch", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "Optional. If set to true, and the Pipeline is not found, a new Pipeline will be created. In this situation, `update_mask` is ignored.", "location": "query", "type": "boolean"}, "name": {"description": "Identifier. The resource name of the Pipeline. Must be unique within the location of the project and must be in `projects/{project}/locations/{location}/pipelines/{pipeline}` format.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/pipelines/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. The fields to be updated; only fields explicitly provided are updated. If no field mask is provided, all provided fields in the request are updated. To update all fields, provide a field mask of \"*\".", "format": "google-fieldmask", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set, validate the request and preview the review, but do not post it.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}", "request": {"$ref": "Pipeline"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/pipelines/{pipelinesId}:setIamPolicy", "httpMethod": "POST", "id": "eventarc.projects.locations.pipelines.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/pipelines/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/pipelines/{pipelinesId}:testIamPermissions", "httpMethod": "POST", "id": "eventarc.projects.locations.pipelines.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/pipelines/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "providers": {"methods": {"get": {"description": "Get a single Provider.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/providers/{providersId}", "httpMethod": "GET", "id": "eventarc.projects.locations.providers.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the provider to get.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/providers/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Provider"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "List providers.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/providers", "httpMethod": "GET", "id": "eventarc.projects.locations.providers.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "The filter field that the list request will filter on.", "location": "query", "type": "string"}, "orderBy": {"description": "The sorting order of the resources returned. Value should be a comma-separated list of fields. The default sorting oder is ascending. To specify descending order for a field, append a `desc` suffix; for example: `name desc, _id`.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of providers to return on each page.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The page token; provide the value from the `next_page_token` field in a previous `ListProviders` call to retrieve the subsequent page. When paginating, all other parameters provided to `ListProviders` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent of the provider to get.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/providers", "response": {"$ref": "ListProvidersResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "triggers": {"methods": {"create": {"description": "Create a new trigger in a particular project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/triggers", "httpMethod": "POST", "id": "eventarc.projects.locations.triggers.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent collection in which to add this trigger.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "triggerId": {"description": "Required. The user-provided ID to be assigned to the trigger.", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set, validate the request and preview the review, but do not post it.", "location": "query", "type": "boolean"}}, "path": "v1/{+parent}/triggers", "request": {"$ref": "<PERSON><PERSON>"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Delete a single trigger.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/triggers/{triggersId}", "httpMethod": "DELETE", "id": "eventarc.projects.locations.triggers.delete", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "If set to true, and the trigger is not found, the request will succeed but no action will be taken on the server.", "location": "query", "type": "boolean"}, "etag": {"description": "If provided, the trigger will only be deleted if the etag matches the current etag on the resource.", "location": "query", "type": "string"}, "name": {"description": "Required. The name of the trigger to be deleted.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/triggers/[^/]+$", "required": true, "type": "string"}, "validateOnly": {"description": "Optional. If set, validate the request and preview the review, but do not post it.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Get a single trigger.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/triggers/{triggersId}", "httpMethod": "GET", "id": "eventarc.projects.locations.triggers.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the trigger to get.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/triggers/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "<PERSON><PERSON>"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/triggers/{triggersId}:getIamPolicy", "httpMethod": "GET", "id": "eventarc.projects.locations.triggers.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/triggers/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "List triggers.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/triggers", "httpMethod": "GET", "id": "eventarc.projects.locations.triggers.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Filter field. Used to filter the Triggers to be listed. Possible filters are described in https://google.aip.dev/160. For example, using \"?filter=destination:gke\" would list only Triggers with a gke destination.", "location": "query", "type": "string"}, "orderBy": {"description": "The sorting order of the resources returned. Value should be a comma-separated list of fields. The default sorting order is ascending. To specify descending order for a field, append a `desc` suffix; for example: `name desc, trigger_id`.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of triggers to return on each page. Note: The service may send fewer.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The page token; provide the value from the `next_page_token` field in a previous `ListTriggers` call to retrieve the subsequent page. When paginating, all other parameters provided to `ListTriggers` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent collection to list triggers on.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/triggers", "response": {"$ref": "ListTriggersResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Update a single trigger.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/triggers/{triggersId}", "httpMethod": "PATCH", "id": "eventarc.projects.locations.triggers.patch", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "If set to true, and the trigger is not found, a new trigger will be created. In this situation, `update_mask` is ignored.", "location": "query", "type": "boolean"}, "name": {"description": "Required. The resource name of the trigger. Must be unique within the location of the project and must be in `projects/{project}/locations/{location}/triggers/{trigger}` format.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/triggers/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "The fields to be updated; only fields explicitly provided are updated. If no field mask is provided, all provided fields in the request are updated. To update all fields, provide a field mask of \"*\".", "format": "google-fieldmask", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set, validate the request and preview the review, but do not post it.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}", "request": {"$ref": "<PERSON><PERSON>"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/triggers/{triggersId}:setIamPolicy", "httpMethod": "POST", "id": "eventarc.projects.locations.triggers.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/triggers/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/triggers/{triggersId}:testIamPermissions", "httpMethod": "POST", "id": "eventarc.projects.locations.triggers.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/triggers/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}, "revision": "20250411", "rootUrl": "https://eventarc.googleapis.com/", "schemas": {"AuditConfig": {"description": "Specifies the audit configuration for a service. The configuration determines which permission types are logged, and what identities, if any, are exempted from logging. An AuditConfig must have one or more AuditLogConfigs. If there are AuditConfigs for both `allServices` and a specific service, the union of the two AuditConfigs is used for that service: the log_types specified in each AuditConfig are enabled, and the exempted_members in each AuditLogConfig are exempted. Example Policy with multiple AuditConfigs: { \"audit_configs\": [ { \"service\": \"allServices\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" }, { \"log_type\": \"ADMIN_READ\" } ] }, { \"service\": \"sampleservice.googleapis.com\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\" }, { \"log_type\": \"DATA_WRITE\", \"exempted_members\": [ \"user:<EMAIL>\" ] } ] } ] } For sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ logging. It also exempts `<EMAIL>` from DATA_READ logging, and `<EMAIL>` from DATA_WRITE logging.", "id": "AuditConfig", "properties": {"auditLogConfigs": {"description": "The configuration for logging of each type of permission.", "items": {"$ref": "AuditLogConfig"}, "type": "array"}, "service": {"description": "Specifies a service that will be enabled for audit logging. For example, `storage.googleapis.com`, `cloudsql.googleapis.com`. `allServices` is a special value that covers all services.", "type": "string"}}, "type": "object"}, "AuditLogConfig": {"description": "Provides the configuration for logging a type of permissions. Example: { \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" } ] } This enables 'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from DATA_READ logging.", "id": "AuditLogConfig", "properties": {"exemptedMembers": {"description": "Specifies the identities that do not cause logging for this type of permission. Follows the same format of Binding.members.", "items": {"type": "string"}, "type": "array"}, "logType": {"description": "The log type that this config enables.", "enum": ["LOG_TYPE_UNSPECIFIED", "ADMIN_READ", "DATA_WRITE", "DATA_READ"], "enumDescriptions": ["Default case. Should never be this.", "Admin reads. Example: CloudIAM getIamPolicy", "Data writes. Example: CloudSQL Users create", "Data reads. Example: CloudSQL Users list"], "type": "string"}}, "type": "object"}, "Binding": {"description": "Associates `members`, or principals, with a `role`.", "id": "Binding", "properties": {"condition": {"$ref": "Expr", "description": "The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies)."}, "members": {"description": "Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workforce identity pool. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`: All workforce identities in a group. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All workforce identities with a specific attribute value. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/*`: All identities in a workforce identity pool. * `principal://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workload identity pool. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/group/{group_id}`: A workload identity pool group. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All identities in a workload identity pool with a certain attribute. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/*`: All identities in a workload identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid}` and the recovered group retains the role in the binding. * `deleted:principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: Deleted single identity in a workforce identity pool. For example, `deleted:principal://iam.googleapis.com/locations/global/workforcePools/my-pool-id/subject/my-subject-attribute-value`.", "items": {"type": "string"}, "type": "array"}, "role": {"description": "Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an overview of the IAM roles and permissions, see the [IAM documentation](https://cloud.google.com/iam/docs/roles-overview). For a list of the available pre-defined roles, see [here](https://cloud.google.com/iam/docs/understanding-roles).", "type": "string"}}, "type": "object"}, "Channel": {"description": "A representation of the Channel resource. A Channel is a resource on which event providers publish their events. The published events are delivered through the transport associated with the channel. Note that a channel is associated with exactly one event provider.", "id": "Channel", "properties": {"activationToken": {"description": "Output only. The activation token for the channel. The token must be used by the provider to register the channel for publishing.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The creation time.", "format": "google-datetime", "readOnly": true, "type": "string"}, "cryptoKeyName": {"description": "Resource name of a KMS crypto key (managed by the user) used to encrypt/decrypt their event data. It must match the pattern `projects/*/locations/*/keyRings/*/cryptoKeys/*`.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Resource labels.", "type": "object"}, "name": {"description": "Required. The resource name of the channel. Must be unique within the location on the project and must be in `projects/{project}/locations/{location}/channels/{channel_id}` format.", "type": "string"}, "provider": {"description": "The name of the event provider (e.g. Eventarc SaaS partner) associated with the channel. This provider will be granted permissions to publish events to the channel. Format: `projects/{project}/locations/{location}/providers/{provider_id}`.", "type": "string"}, "pubsubTopic": {"description": "Output only. The name of the Pub/Sub topic created and managed by Eventarc system as a transport for the event delivery. Format: `projects/{project}/topics/{topic_id}`.", "readOnly": true, "type": "string"}, "satisfiesPzs": {"description": "Output only. Whether or not this Channel satisfies the requirements of physical zone separation", "readOnly": true, "type": "boolean"}, "state": {"description": "Output only. The state of a Channel.", "enum": ["STATE_UNSPECIFIED", "PENDING", "ACTIVE", "INACTIVE"], "enumDescriptions": ["Default value. This value is unused.", "The PENDING state indicates that a Channel has been created successfully and there is a new activation token available for the subscriber to use to convey the Channel to the provider in order to create a Connection.", "The ACTIVE state indicates that a Channel has been successfully connected with the event provider. An ACTIVE Channel is ready to receive and route events from the event provider.", "The INACTIVE state indicates that the Channel cannot receive events permanently. There are two possible cases this state can happen: 1. The SaaS provider disconnected from this Channel. 2. The Channel activation token has expired but the SaaS provider wasn't connected. To re-establish a Connection with a provider, the subscriber should create a new Channel and give it to the provider."], "readOnly": true, "type": "string"}, "uid": {"description": "Output only. Server assigned unique identifier for the channel. The value is a UUID4 string and guaranteed to remain unchanged until the resource is deleted.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The last-modified time.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "ChannelConnection": {"description": "A representation of the ChannelConnection resource. A ChannelConnection is a resource which event providers create during the activation process to establish a connection between the provider and the subscriber channel.", "id": "ChannelConnection", "properties": {"activationToken": {"description": "Input only. Activation token for the channel. The token will be used during the creation of ChannelConnection to bind the channel with the provider project. This field will not be stored in the provider resource.", "type": "string"}, "channel": {"description": "Required. The name of the connected subscriber Channel. This is a weak reference to avoid cross project and cross accounts references. This must be in `projects/{project}/location/{location}/channels/{channel_id}` format.", "type": "string"}, "createTime": {"description": "Output only. The creation time.", "format": "google-datetime", "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Resource labels.", "type": "object"}, "name": {"description": "Required. The name of the connection.", "type": "string"}, "uid": {"description": "Output only. Server assigned ID of the resource. The server guarantees uniqueness and immutability until deleted.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The last-modified time.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "CloudRun": {"description": "Represents a Cloud Run destination.", "id": "CloudRun", "properties": {"path": {"description": "Optional. The relative path on the Cloud Run service the events should be sent to. The value must conform to the definition of a URI path segment (section 3.3 of RFC2396). Examples: \"/route\", \"route\", \"route/subroute\".", "type": "string"}, "region": {"description": "Required. The region the Cloud Run service is deployed in.", "type": "string"}, "service": {"description": "Required. The name of the Cloud Run service being addressed. See https://cloud.google.com/run/docs/reference/rest/v1/namespaces.services. Only services located in the same project as the trigger object can be addressed.", "type": "string"}}, "type": "object"}, "Destination": {"description": "Represents a target of an invocation over HTTP.", "id": "Destination", "properties": {"cloudFunction": {"description": "The Cloud Function resource name. Cloud Functions V1 and V2 are supported. Format: `projects/{project}/locations/{location}/functions/{function}` This is a read-only field. Creating Cloud Functions V1/V2 triggers is only supported via the Cloud Functions product. An error will be returned if the user sets this value.", "type": "string"}, "cloudRun": {"$ref": "CloudRun", "description": "Cloud Run fully-managed resource that receives the events. The resource should be in the same project as the trigger."}, "gke": {"$ref": "GKE", "description": "A GKE service capable of receiving events. The service should be running in the same project as the trigger."}, "httpEndpoint": {"$ref": "HttpEndpoint", "description": "An HTTP endpoint destination described by an URI."}, "networkConfig": {"$ref": "NetworkConfig", "description": "Optional. Network config is used to configure how Eventarc resolves and connect to a destination. This should only be used with HttpEndpoint destination type."}, "workflow": {"description": "The resource name of the Workflow whose Executions are triggered by the events. The Workflow resource should be deployed in the same project as the trigger. Format: `projects/{project}/locations/{location}/workflows/{workflow}`", "type": "string"}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "Enrollment": {"description": "An enrollment represents a subscription for messages on a particular message bus. It defines a matching criteria for messages on the bus and the subscriber endpoint where matched messages should be delivered.", "id": "Enrollment", "properties": {"annotations": {"additionalProperties": {"type": "string"}, "description": "Optional. Resource annotations.", "type": "object"}, "celMatch": {"description": "Required. A CEL expression identifying which messages this enrollment applies to.", "type": "string"}, "createTime": {"description": "Output only. The creation time.", "format": "google-datetime", "readOnly": true, "type": "string"}, "destination": {"description": "Required. Destination is the Pipeline that the Enrollment is delivering to. It must point to the full resource name of a Pipeline. Format: \"projects/{PROJECT_ID}/locations/{region}/pipelines/{PIPELINE_ID)\"", "type": "string"}, "displayName": {"description": "Optional. Resource display name.", "type": "string"}, "etag": {"description": "Output only. This checksum is computed by the server based on the value of other fields, and might be sent only on update and delete requests to ensure that the client has an up-to-date value before proceeding.", "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Resource labels.", "type": "object"}, "messageBus": {"description": "Required. Immutable. Resource name of the message bus identifying the source of the messages. It matches the form projects/{project}/locations/{location}/messageBuses/{messageBus}.", "type": "string"}, "name": {"description": "Identifier. Resource name of the form projects/{project}/locations/{location}/enrollments/{enrollment}", "type": "string"}, "uid": {"description": "Output only. Server assigned unique identifier for the channel. The value is a UUID4 string and guaranteed to remain unchanged until the resource is deleted.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The last-modified time.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "EventFilter": {"description": "Filters events based on exact matches on the CloudEvents attributes.", "id": "EventFilter", "properties": {"attribute": {"description": "Required. The name of a CloudEvents attribute. Currently, only a subset of attributes are supported for filtering. You can [retrieve a specific provider's supported event types](/eventarc/docs/list-providers#describe-provider). All triggers MUST provide a filter for the 'type' attribute.", "type": "string"}, "operator": {"description": "Optional. The operator used for matching the events with the value of the filter. If not specified, only events that have an exact key-value pair specified in the filter are matched. The allowed values are `path_pattern` and `match-path-pattern`. `path_pattern` is only allowed for GCFv1 triggers.", "type": "string"}, "value": {"description": "Required. The value for the attribute.", "type": "string"}}, "type": "object"}, "EventType": {"description": "A representation of the event type resource.", "id": "EventType", "properties": {"description": {"description": "Output only. Human friendly description of what the event type is about. For example \"Bucket created in Cloud Storage\".", "readOnly": true, "type": "string"}, "eventSchemaUri": {"description": "Output only. URI for the event schema. For example \"https://github.com/googleapis/google-cloudevents/blob/master/proto/google/events/cloud/storage/v1/events.proto\"", "readOnly": true, "type": "string"}, "filteringAttributes": {"description": "Output only. Filtering attributes for the event type.", "items": {"$ref": "FilteringAttribute"}, "readOnly": true, "type": "array"}, "type": {"description": "Output only. The full name of the event type (for example, \"google.cloud.storage.object.v1.finalized\"). In the form of {provider-specific-prefix}.{resource}.{version}.{verb}. Types MUST be versioned and event schemas are guaranteed to remain backward compatible within one version. Note that event type versions and API versions do not need to match.", "readOnly": true, "type": "string"}}, "type": "object"}, "Expr": {"description": "Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: \"Summary size limit\" description: \"Determines if a summary is less than 100 chars\" expression: \"document.summary.size() < 100\" Example (Equality): title: \"Requestor is owner\" description: \"Determines if requestor is the document owner\" expression: \"document.owner == request.auth.claims.email\" Example (Logic): title: \"Public documents\" description: \"Determine whether the document should be publicly visible\" expression: \"document.type != 'private' && document.type != 'internal'\" Example (Data Manipulation): title: \"Notification string\" description: \"Create a notification string with a timestamp.\" expression: \"'New message received at ' + string(document.create_time)\" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.", "id": "Expr", "properties": {"description": {"description": "Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.", "type": "string"}, "expression": {"description": "Textual representation of an expression in Common Expression Language syntax.", "type": "string"}, "location": {"description": "Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.", "type": "string"}, "title": {"description": "Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.", "type": "string"}}, "type": "object"}, "FilteringAttribute": {"description": "A representation of the FilteringAttribute resource. Filtering attributes are per event type.", "id": "FilteringAttribute", "properties": {"attribute": {"description": "Output only. Attribute used for filtering the event type.", "readOnly": true, "type": "string"}, "description": {"description": "Output only. Description of the purpose of the attribute.", "readOnly": true, "type": "string"}, "pathPatternSupported": {"description": "Output only. If true, the attribute accepts matching expressions in the Eventarc PathPattern format.", "readOnly": true, "type": "boolean"}, "required": {"description": "Output only. If true, the triggers for this provider should always specify a filter on these attributes. Trigger creation will fail otherwise.", "readOnly": true, "type": "boolean"}}, "type": "object"}, "GKE": {"description": "Represents a GKE destination.", "id": "GKE", "properties": {"cluster": {"description": "Required. The name of the cluster the GKE service is running in. The cluster must be running in the same project as the trigger being created.", "type": "string"}, "location": {"description": "Required. The name of the Google Compute Engine in which the cluster resides, which can either be compute zone (for example, us-central1-a) for the zonal clusters or region (for example, us-central1) for regional clusters.", "type": "string"}, "namespace": {"description": "Required. The namespace the GKE service is running in.", "type": "string"}, "path": {"description": "Optional. The relative path on the GKE service the events should be sent to. The value must conform to the definition of a URI path segment (section 3.3 of RFC2396). Examples: \"/route\", \"route\", \"route/subroute\".", "type": "string"}, "service": {"description": "Required. Name of the GKE service.", "type": "string"}}, "type": "object"}, "GoogleApiSource": {"description": "A GoogleApiSource represents a subscription of 1P events from a MessageBus.", "id": "GoogleApiSource", "properties": {"annotations": {"additionalProperties": {"type": "string"}, "description": "Optional. Resource annotations.", "type": "object"}, "createTime": {"description": "Output only. The creation time.", "format": "google-datetime", "readOnly": true, "type": "string"}, "cryptoKeyName": {"description": "Optional. Resource name of a KMS crypto key (managed by the user) used to encrypt/decrypt their event data. It must match the pattern `projects/*/locations/*/keyRings/*/cryptoKeys/*`.", "type": "string"}, "destination": {"description": "Required. Destination is the message bus that the GoogleApiSource is delivering to. It must be point to the full resource name of a MessageBus. Format: \"projects/{PROJECT_ID}/locations/{region}/messagesBuses/{MESSAGE_BUS_ID)", "type": "string"}, "displayName": {"description": "Optional. Resource display name.", "type": "string"}, "etag": {"description": "Output only. This checksum is computed by the server based on the value of other fields, and might be sent only on update and delete requests to ensure that the client has an up-to-date value before proceeding.", "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Resource labels.", "type": "object"}, "loggingConfig": {"$ref": "LoggingConfig", "description": "Optional. Config to control Platform logging for the GoogleApiSource."}, "name": {"description": "Identifier. Resource name of the form projects/{project}/locations/{location}/googleApiSources/{google_api_source}", "type": "string"}, "uid": {"description": "Output only. Server assigned unique identifier for the channel. The value is a UUID4 string and guaranteed to remain unchanged until the resource is deleted.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The last-modified time.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleChannelConfig": {"description": "A GoogleChannelConfig is a resource that stores the custom settings respected by Eventarc first-party triggers in the matching region. Once configured, first-party event data will be protected using the specified custom managed encryption key instead of Google-managed encryption keys.", "id": "GoogleChannelConfig", "properties": {"cryptoKeyName": {"description": "Optional. Resource name of a KMS crypto key (managed by the user) used to encrypt/decrypt their event data. It must match the pattern `projects/*/locations/*/keyRings/*/cryptoKeys/*`.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Resource labels.", "type": "object"}, "name": {"description": "Required. The resource name of the config. Must be in the format of, `projects/{project}/locations/{location}/googleChannelConfig`. In API responses, the config name always includes the projectID, regardless of whether the projectID or projectNumber was provided.", "type": "string"}, "updateTime": {"description": "Output only. The last-modified time.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudEventarcV1PipelineDestination": {"description": "Represents a target of an invocation over HTTP.", "id": "GoogleCloudEventarcV1PipelineDestination", "properties": {"authenticationConfig": {"$ref": "GoogleCloudEventarcV1PipelineDestinationAuthenticationConfig", "description": "Optional. An authentication config used to authenticate message requests, such that destinations can verify the source. For example, this can be used with private GCP destinations that require GCP credentials to access like Cloud Run. This field is optional and should be set only by users interested in authenticated push"}, "httpEndpoint": {"$ref": "GoogleCloudEventarcV1PipelineDestinationHttpEndpoint", "description": "Optional. An HTTP endpoint destination described by an URI. If a DNS FQDN is provided as the endpoint, Pipeline will create a peering zone to the consumer VPC and forward DNS requests to the VPC specified by network config to resolve the service endpoint. See: https://cloud.google.com/dns/docs/zones/zones-overview#peering_zones"}, "messageBus": {"description": "Optional. The resource name of the Message Bus to which events should be published. The Message Bus resource should exist in the same project as the Pipeline. Format: `projects/{project}/locations/{location}/messageBuses/{message_bus}`", "type": "string"}, "networkConfig": {"$ref": "GoogleCloudEventarcV1PipelineDestinationNetworkConfig", "description": "Optional. Network config is used to configure how Pipeline resolves and connects to a destination."}, "outputPayloadFormat": {"$ref": "GoogleCloudEventarcV1PipelineMessagePayloadFormat", "description": "Optional. The message format before it is delivered to the destination. If not set, the message will be delivered in the format it was originally delivered to the Pipeline. This field can only be set if Pipeline.input_payload_format is also set."}, "topic": {"description": "Optional. The resource name of the Pub/Sub topic to which events should be published. Format: `projects/{project}/locations/{location}/topics/{topic}`", "type": "string"}, "workflow": {"description": "Optional. The resource name of the Workflow whose Executions are triggered by the events. The Workflow resource should be deployed in the same project as the Pipeline. Format: `projects/{project}/locations/{location}/workflows/{workflow}`", "type": "string"}}, "type": "object"}, "GoogleCloudEventarcV1PipelineDestinationAuthenticationConfig": {"description": "Represents a config used to authenticate message requests.", "id": "GoogleCloudEventarcV1PipelineDestinationAuthenticationConfig", "properties": {"googleOidc": {"$ref": "GoogleCloudEventarcV1PipelineDestinationAuthenticationConfigOidcToken", "description": "Optional. This authenticate method will apply Google OIDC tokens signed by a GCP service account to the requests."}, "oauthToken": {"$ref": "GoogleCloudEventarcV1PipelineDestinationAuthenticationConfigOAuthToken", "description": "Optional. If specified, an [OAuth token](https://developers.google.com/identity/protocols/OAuth2) will be generated and attached as an `Authorization` header in the HTTP request. This type of authorization should generally only be used when calling Google APIs hosted on *.googleapis.com."}}, "type": "object"}, "GoogleCloudEventarcV1PipelineDestinationAuthenticationConfigOAuthToken": {"description": "Contains information needed for generating an [OAuth token](https://developers.google.com/identity/protocols/OAuth2). This type of authorization should generally only be used when calling Google APIs hosted on *.googleapis.com.", "id": "GoogleCloudEventarcV1PipelineDestinationAuthenticationConfigOAuthToken", "properties": {"scope": {"description": "Optional. OAuth scope to be used for generating OAuth access token. If not specified, \"https://www.googleapis.com/auth/cloud-platform\" will be used.", "type": "string"}, "serviceAccount": {"description": "Required. Service account email used to generate the [OAuth token](https://developers.google.com/identity/protocols/OAuth2). The principal who calls this API must have iam.serviceAccounts.actAs permission in the service account. See https://cloud.google.com/iam/docs/understanding-service-accounts for more information. Eventarc service agents must have roles/roles/iam.serviceAccountTokenCreator role to allow Pipeline to create OAuth2 tokens for authenticated requests.", "type": "string"}}, "type": "object"}, "GoogleCloudEventarcV1PipelineDestinationAuthenticationConfigOidcToken": {"description": "Represents a config used to authenticate with a Google OIDC token using a GCP service account. Use this authentication method to invoke your Cloud Run and Cloud Functions destinations or HTTP endpoints that support Google OIDC.", "id": "GoogleCloudEventarcV1PipelineDestinationAuthenticationConfigOidcToken", "properties": {"audience": {"description": "Optional. Audience to be used to generate the OIDC Token. The audience claim identifies the recipient that the JWT is intended for. If unspecified, the destination URI will be used.", "type": "string"}, "serviceAccount": {"description": "Required. Service account email used to generate the OIDC Token. The principal who calls this API must have iam.serviceAccounts.actAs permission in the service account. See https://cloud.google.com/iam/docs/understanding-service-accounts for more information. Eventarc service agents must have roles/roles/iam.serviceAccountTokenCreator role to allow the Pipeline to create OpenID tokens for authenticated requests.", "type": "string"}}, "type": "object"}, "GoogleCloudEventarcV1PipelineDestinationHttpEndpoint": {"description": "Represents a HTTP endpoint destination.", "id": "GoogleCloudEventarcV1PipelineDestinationHttpEndpoint", "properties": {"messageBindingTemplate": {"description": "Optional. The CEL expression used to modify how the destination-bound HTTP request is constructed. If a binding expression is not specified here, the message is treated as a CloudEvent and is mapped to the HTTP request according to the CloudEvent HTTP Protocol Binding Binary Content Mode (https://github.com/cloudevents/spec/blob/main/cloudevents/bindings/http-protocol-binding.md#31-binary-content-mode). In this representation, all fields except the `data` and `datacontenttype` field on the message are mapped to HTTP request headers with a prefix of `ce-`. To construct the HTTP request payload and the value of the content-type HTTP header, the payload format is defined as follows: 1) Use the output_payload_format_type on the Pipeline.Destination if it is set, else: 2) Use the input_payload_format_type on the Pipeline if it is set, else: 3) Treat the payload as opaque binary data. The `data` field of the message is converted to the payload format or left as-is for case 3) and then attached as the payload of the HTTP request. The `content-type` header on the HTTP request is set to the payload format type or left empty for case 3). However, if a mediation has updated the `datacontenttype` field on the message so that it is not the same as the payload format type but it is still a prefix of the payload format type, then the `content-type` header on the HTTP request is set to this `datacontenttype` value. For example, if the `datacontenttype` is \"application/json\" and the payload format type is \"application/json; charset=utf-8\", then the `content-type` header on the HTTP request is set to \"application/json; charset=utf-8\". If a non-empty binding expression is specified then this expression is used to modify the default CloudEvent HTTP Protocol Binding Binary Content representation. The result of the CEL expression must be a map of key/value pairs which is used as follows: - If a map named `headers` exists on the result of the expression, then its key/value pairs are directly mapped to the HTTP request headers. The headers values are constructed from the corresponding value type's canonical representation. If the `headers` field doesn't exist then the resulting HTTP request will be the headers of the CloudEvent HTTP Binding Binary Content Mode representation of the final message. Note: If the specified binding expression, has updated the `datacontenttype` field on the message so that it is not the same as the payload format type but it is still a prefix of the payload format type, then the `content-type` header in the `headers` map is set to this `datacontenttype` value. - If a field named `body` exists on the result of the expression then its value is directly mapped to the body of the request. If the value of the `body` field is of type bytes or string then it is used for the HTTP request body as-is, with no conversion. If the body field is of any other type then it is converted to a JSON string. If the body field does not exist then the resulting payload of the HTTP request will be data value of the CloudEvent HTTP Binding Binary Content Mode representation of the final message as described earlier. - Any other fields in the resulting expression will be ignored. The CEL expression may access the incoming CloudEvent message in its definition, as follows: - The `data` field of the incoming CloudEvent message can be accessed using the `message.data` value. Subfields of `message.data` may also be accessed if an input_payload_format has been specified on the Pipeline. - Each attribute of the incoming CloudEvent message can be accessed using the `message.` value, where is replaced with the name of the attribute. - Existing headers can be accessed in the CEL expression using the `headers` variable. The `headers` variable defines a map of key/value pairs corresponding to the HTTP headers of the CloudEvent HTTP Binding Binary Content Mode representation of the final message as described earlier. For example, the following CEL expression can be used to construct an HTTP request by adding an additional header to the HTTP headers of the CloudEvent HTTP Binding Binary Content Mode representation of the final message and by overwriting the body of the request: ``` { \"headers\": headers.merge({\"new-header-key\": \"new-header-value\"}), \"body\": \"new-body\" } ``` - The default binding for the message payload can be accessed using the `body` variable. It conatins a string representation of the message payload in the format specified by the `output_payload_format` field. If the `input_payload_format` field is not set, the `body` variable contains the same message payload bytes that were published. Additionally, the following CEL extension functions are provided for use in this CEL expression: - toBase64Url: map.toBase64Url() -> string - Converts a CelValue to a base64url encoded string - toJsonString: map.toJsonString() -> string - Converts a CelValue to a JSON string - merge: map1.merge(map2) -> map3 - Merges the passed CEL map with the existing CEL map the function is applied to. - If the same key exists in both maps, if the key's value is type map both maps are merged else the value from the passed map is used. - denormalize: map.denormalize() -> map - Denormalizes a CEL map such that every value of type map or key in the map is expanded to return a single level map. - The resulting keys are \".\" separated indices of the map keys. - For example: { \"a\": 1, \"b\": { \"c\": 2, \"d\": 3 } \"e\": [4, 5] } .denormalize() -> { \"a\": 1, \"b.c\": 2, \"b.d\": 3, \"e.0\": 4, \"e.1\": 5 } - setField: map.setField(key, value) -> message - Sets the field of the message with the given key to the given value. - If the field is not present it will be added. - If the field is present it will be overwritten. - The key can be a dot separated path to set a field in a nested message. - Key must be of type string. - Value may be any valid type. - removeFields: map.removeFields([key1, key2, ...]) -> message - Removes the fields of the map with the given keys. - The keys can be a dot separated path to remove a field in a nested message. - If a key is not found it will be ignored. - Keys must be of type string. - toMap: [map1, map2, ...].toMap() -> map - Converts a CEL list of CEL maps to a single CEL map - toCloudEventJsonWithPayloadFormat: message.toCloudEventJsonWithPayloadFormat() -> map - Converts a message to the corresponding structure of JSON format for CloudEvents. - It converts `data` to destination payload format specified in `output_payload_format`. If `output_payload_format` is not set, the data will remain unchanged. - It also sets the corresponding datacontenttype of the CloudEvent, as indicated by `output_payload_format`. If no `output_payload_format` is set it will use the value of the \"datacontenttype\" attribute on the CloudEvent if present, else remove \"datacontenttype\" attribute. - This function expects that the content of the message will adhere to the standard CloudEvent format. If it doesn't then this function will fail. - The result is a CEL map that corresponds to the JSON representation of the CloudEvent. To convert that data to a JSON string it can be chained with the toJsonString function. The Pipeline expects that the message it receives adheres to the standard CloudEvent format. If it doesn't then the outgoing message request may fail with a persistent error.", "type": "string"}, "uri": {"description": "Required. The URI of the HTTP endpoint. The value must be a RFC2396 URI string. Examples: `https://svc.us-central1.p.local:8080/route`. Only the HTTPS protocol is supported.", "type": "string"}}, "type": "object"}, "GoogleCloudEventarcV1PipelineDestinationNetworkConfig": {"description": "Represents a network config to be used for destination resolution and connectivity.", "id": "GoogleCloudEventarcV1PipelineDestinationNetworkConfig", "properties": {"networkAttachment": {"description": "Required. Name of the NetworkAttachment that allows access to the consumer VPC. Format: `projects/{PROJECT_ID}/regions/{REGION}/networkAttachments/{NETWORK_ATTACHMENT_NAME}`", "type": "string"}}, "type": "object"}, "GoogleCloudEventarcV1PipelineMediation": {"description": "Mediation defines different ways to modify the Pipeline.", "id": "GoogleCloudEventarcV1PipelineMediation", "properties": {"transformation": {"$ref": "GoogleCloudEventarcV1PipelineMediationTransformation", "description": "Optional. How the Pipeline is to transform messages"}}, "type": "object"}, "GoogleCloudEventarcV1PipelineMediationTransformation": {"description": "Transformation defines the way to transform an incoming message.", "id": "GoogleCloudEventarcV1PipelineMediationTransformation", "properties": {"transformationTemplate": {"description": "Optional. The CEL expression template to apply to transform messages. The following CEL extension functions are provided for use in this CEL expression: - merge: map1.merge(map2) -> map3 - Merges the passed CEL map with the existing CEL map the function is applied to. - If the same key exists in both maps, if the key's value is type map both maps are merged else the value from the passed map is used. - denormalize: map.denormalize() -> map - Denormalizes a CEL map such that every value of type map or key in the map is expanded to return a single level map. - The resulting keys are \".\" separated indices of the map keys. - For example: { \"a\": 1, \"b\": { \"c\": 2, \"d\": 3 } \"e\": [4, 5] } .denormalize() -> { \"a\": 1, \"b.c\": 2, \"b.d\": 3, \"e.0\": 4, \"e.1\": 5 } - setField: map.setField(key, value) -> message - Sets the field of the message with the given key to the given value. - If the field is not present it will be added. - If the field is present it will be overwritten. - The key can be a dot separated path to set a field in a nested message. - Key must be of type string. - Value may be any valid type. - removeFields: map.removeFields([key1, key2, ...]) -> message - Removes the fields of the map with the given keys. - The keys can be a dot separated path to remove a field in a nested message. - If a key is not found it will be ignored. - Keys must be of type string. - toMap: [map1, map2, ...].toMap() -> map - Converts a CEL list of CEL maps to a single CEL map - toDestinationPayloadFormat(): message.data.toDestinationPayloadFormat() -> string or bytes - Converts the message data to the destination payload format specified in Pipeline.Destination.output_payload_format - This function is meant to be applied to the message.data field. - If the destination payload format is not set, the function will return the message data unchanged. - toCloudEventJsonWithPayloadFormat: message.toCloudEventJsonWithPayloadFormat() -> map - Converts a message to the corresponding structure of JSON format for CloudEvents - This function applies toDestinationPayloadFormat() to the message data. It also sets the corresponding datacontenttype of the CloudEvent, as indicated by Pipeline.Destination.output_payload_format. If no output_payload_format is set it will use the existing datacontenttype on the CloudEvent if present, else leave datacontenttype absent. - This function expects that the content of the message will adhere to the standard CloudEvent format. If it doesn't then this function will fail. - The result is a CEL map that corresponds to the JSON representation of the CloudEvent. To convert that data to a JSON string it can be chained with the toJsonString function.", "type": "string"}}, "type": "object"}, "GoogleCloudEventarcV1PipelineMessagePayloadFormat": {"description": "Represents the format of message data.", "id": "GoogleCloudEventarcV1PipelineMessagePayloadFormat", "properties": {"avro": {"$ref": "GoogleCloudEventarcV1PipelineMessagePayloadFormatAvroFormat", "description": "Optional. Avro format."}, "json": {"$ref": "GoogleCloudEventarcV1PipelineMessagePayloadFormatJsonFormat", "description": "Optional. JSON format."}, "protobuf": {"$ref": "GoogleCloudEventarcV1PipelineMessagePayloadFormatProtobufFormat", "description": "Optional. Protobuf format."}}, "type": "object"}, "GoogleCloudEventarcV1PipelineMessagePayloadFormatAvroFormat": {"description": "The format of an AVRO message payload.", "id": "GoogleCloudEventarcV1PipelineMessagePayloadFormatAvroFormat", "properties": {"schemaDefinition": {"description": "Optional. The entire schema definition is stored in this field.", "type": "string"}}, "type": "object"}, "GoogleCloudEventarcV1PipelineMessagePayloadFormatJsonFormat": {"description": "The format of a JSON message payload.", "id": "GoogleCloudEventarcV1PipelineMessagePayloadFormatJsonFormat", "properties": {}, "type": "object"}, "GoogleCloudEventarcV1PipelineMessagePayloadFormatProtobufFormat": {"description": "The format of a Protobuf message payload.", "id": "GoogleCloudEventarcV1PipelineMessagePayloadFormatProtobufFormat", "properties": {"schemaDefinition": {"description": "Optional. The entire schema definition is stored in this field.", "type": "string"}}, "type": "object"}, "GoogleCloudEventarcV1PipelineRetryPolicy": {"description": "The retry policy configuration for the Pipeline. The pipeline exponentially backs off in case the destination is non responsive or returns a retryable error code. The default semantics are as follows: The backoff starts with a 5 second delay and doubles the delay after each failed attempt (10 seconds, 20 seconds, 40 seconds, etc.). The delay is capped at 60 seconds by default. Please note that if you set the min_retry_delay and max_retry_delay fields to the same value this will make the duration between retries constant.", "id": "GoogleCloudEventarcV1PipelineRetryPolicy", "properties": {"maxAttempts": {"description": "Optional. The maximum number of delivery attempts for any message. The value must be between 1 and 100. The default value for this field is 5.", "format": "int32", "type": "integer"}, "maxRetryDelay": {"description": "Optional. The maximum amount of seconds to wait between retry attempts. The value must be between 1 and 600. The default value for this field is 60.", "format": "google-duration", "type": "string"}, "minRetryDelay": {"description": "Optional. The minimum amount of seconds to wait between retry attempts. The value must be between 1 and 600. The default value for this field is 5.", "format": "google-duration", "type": "string"}}, "type": "object"}, "GoogleLongrunningCancelOperationRequest": {"description": "The request message for Operations.CancelOperation.", "id": "GoogleLongrunningCancelOperationRequest", "properties": {}, "type": "object"}, "GoogleLongrunningListOperationsResponse": {"description": "The response message for Operations.ListOperations.", "id": "GoogleLongrunningListOperationsResponse", "properties": {"nextPageToken": {"description": "The standard List next-page token.", "type": "string"}, "operations": {"description": "A list of operations that matches the specified filter in the request.", "items": {"$ref": "GoogleLongrunningOperation"}, "type": "array"}}, "type": "object"}, "GoogleLongrunningOperation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "GoogleLongrunningOperation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "GoogleRpcStatus", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "GoogleRpcStatus": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "GoogleRpcStatus", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "HttpEndpoint": {"description": "Represents a HTTP endpoint destination.", "id": "HttpEndpoint", "properties": {"uri": {"description": "Required. The URI of the HTTP endpoint. The value must be a RFC2396 URI string. Examples: `http://**********:80/route`, `http://svc.us-central1.p.local:8080/`. Only HTTP and HTTPS protocols are supported. The host can be either a static IP addressable from the VPC specified by the network config, or an internal DNS hostname of the service resolvable via Cloud DNS.", "type": "string"}}, "type": "object"}, "ListChannelConnectionsResponse": {"description": "The response message for the `ListChannelConnections` method.", "id": "ListChannelConnectionsResponse", "properties": {"channelConnections": {"description": "The requested channel connections, up to the number specified in `page_size`.", "items": {"$ref": "ChannelConnection"}, "type": "array"}, "nextPageToken": {"description": "A page token that can be sent to `ListChannelConnections` to request the next page. If this is empty, then there are no more pages.", "type": "string"}, "unreachable": {"description": "Unreachable resources, if any.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListChannelsResponse": {"description": "The response message for the `ListChannels` method.", "id": "ListChannelsResponse", "properties": {"channels": {"description": "The requested channels, up to the number specified in `page_size`.", "items": {"$ref": "Channel"}, "type": "array"}, "nextPageToken": {"description": "A page token that can be sent to `ListChannels` to request the next page. If this is empty, then there are no more pages.", "type": "string"}, "unreachable": {"description": "Unreachable resources, if any.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListEnrollmentsResponse": {"description": "The response message for the `ListEnrollments` method.", "id": "ListEnrollmentsResponse", "properties": {"enrollments": {"description": "The requested Enrollments, up to the number specified in `page_size`.", "items": {"$ref": "Enrollment"}, "type": "array"}, "nextPageToken": {"description": "A page token that can be sent to `ListEnrollments` to request the next page. If this is empty, then there are no more pages.", "type": "string"}, "unreachable": {"description": "Unreachable resources, if any.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListGoogleApiSourcesResponse": {"description": "The response message for the `ListGoogleApiSources` method.", "id": "ListGoogleApiSourcesResponse", "properties": {"googleApiSources": {"description": "The requested GoogleApiSources, up to the number specified in `page_size`.", "items": {"$ref": "GoogleApiSource"}, "type": "array"}, "nextPageToken": {"description": "A page token that can be sent to `ListMessageBusEnrollments` to request the next page. If this is empty, then there are no more pages.", "type": "string"}, "unreachable": {"description": "Unreachable resources, if any.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListLocationsResponse": {"description": "The response message for Locations.ListLocations.", "id": "ListLocationsResponse", "properties": {"locations": {"description": "A list of locations that matches the specified filter in the request.", "items": {"$ref": "Location"}, "type": "array"}, "nextPageToken": {"description": "The standard List next-page token.", "type": "string"}}, "type": "object"}, "ListMessageBusEnrollmentsResponse": {"description": "The response message for the `ListMessageBusEnrollments` method.`", "id": "ListMessageBusEnrollmentsResponse", "properties": {"enrollments": {"description": "The requested enrollments, up to the number specified in `page_size`.", "items": {"type": "string"}, "type": "array"}, "nextPageToken": {"description": "A page token that can be sent to `ListMessageBusEnrollments` to request the next page. If this is empty, then there are no more pages.", "type": "string"}, "unreachable": {"description": "Unreachable resources, if any.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListMessageBusesResponse": {"description": "The response message for the `ListMessageBuses` method.", "id": "ListMessageBusesResponse", "properties": {"messageBuses": {"description": "The requested message buses, up to the number specified in `page_size`.", "items": {"$ref": "MessageBus"}, "type": "array"}, "nextPageToken": {"description": "A page token that can be sent to `ListMessageBuses` to request the next page. If this is empty, then there are no more pages.", "type": "string"}, "unreachable": {"description": "Unreachable resources, if any.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListPipelinesResponse": {"description": "The response message for the ListPipelines method.", "id": "ListPipelinesResponse", "properties": {"nextPageToken": {"description": "A page token that can be sent to `ListPipelines` to request the next page. If this is empty, then there are no more pages.", "type": "string"}, "pipelines": {"description": "The requested pipelines, up to the number specified in `page_size`.", "items": {"$ref": "Pipeline"}, "type": "array"}, "unreachable": {"description": "Unreachable resources, if any.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListProvidersResponse": {"description": "The response message for the `ListProviders` method.", "id": "ListProvidersResponse", "properties": {"nextPageToken": {"description": "A page token that can be sent to `ListProviders` to request the next page. If this is empty, then there are no more pages.", "type": "string"}, "providers": {"description": "The requested providers, up to the number specified in `page_size`.", "items": {"$ref": "Provider"}, "type": "array"}, "unreachable": {"description": "Unreachable resources, if any.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListTriggersResponse": {"description": "The response message for the `ListTriggers` method.", "id": "ListTriggersResponse", "properties": {"nextPageToken": {"description": "A page token that can be sent to `ListTriggers` to request the next page. If this is empty, then there are no more pages.", "type": "string"}, "triggers": {"description": "The requested triggers, up to the number specified in `page_size`.", "items": {"$ref": "<PERSON><PERSON>"}, "type": "array"}, "unreachable": {"description": "Unreachable resources, if any.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "Location": {"description": "A resource that represents a Google Cloud location.", "id": "Location", "properties": {"displayName": {"description": "The friendly name for this location, typically a nearby city name. For example, \"Tokyo\".", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Cross-service attributes for the location. For example {\"cloud.googleapis.com/region\": \"us-east1\"}", "type": "object"}, "locationId": {"description": "The canonical id for this location. For example: `\"us-east1\"`.", "type": "string"}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata. For example the available capacity at the given location.", "type": "object"}, "name": {"description": "Resource name for the location, which may vary between implementations. For example: `\"projects/example-project/locations/us-east1\"`", "type": "string"}}, "type": "object"}, "LoggingConfig": {"description": "The configuration for Platform Telemetry logging for Eventarc Advanced resources.", "id": "LoggingConfig", "properties": {"logSeverity": {"description": "Optional. The minimum severity of logs that will be sent to Stackdriver/Platform Telemetry. Logs at severitiy ≥ this value will be sent, unless it is NONE.", "enum": ["LOG_SEVERITY_UNSPECIFIED", "NONE", "DEBUG", "INFO", "NOTICE", "WARNING", "ERROR", "CRITICAL", "ALERT", "EMERGENCY"], "enumDescriptions": ["Log severity is not specified. This value is treated the same as NONE, but is used to distinguish between no update and update to NONE in update_masks.", "Default value at resource creation, presence of this value must be treated as no logging/disable logging.", "Debug or trace level logging.", "Routine information, such as ongoing status or performance.", "Normal but significant events, such as start up, shut down, or a configuration change.", "Warning events might cause problems.", "Error events are likely to cause problems.", "Critical events cause more severe problems or outages.", "A person must take action immediately.", "One or more systems are unusable."], "type": "string"}}, "type": "object"}, "MessageBus": {"description": "MessageBus for the messages flowing through the system. The admin has visibility and control over the messages being published and consumed and can restrict publishers and subscribers to only a subset of data available in the system by defining authorization policies.", "id": "MessageBus", "properties": {"annotations": {"additionalProperties": {"type": "string"}, "description": "Optional. Resource annotations.", "type": "object"}, "createTime": {"description": "Output only. The creation time.", "format": "google-datetime", "readOnly": true, "type": "string"}, "cryptoKeyName": {"description": "Optional. Resource name of a KMS crypto key (managed by the user) used to encrypt/decrypt their event data. It must match the pattern `projects/*/locations/*/keyRings/*/cryptoKeys/*`.", "type": "string"}, "displayName": {"description": "Optional. Resource display name.", "type": "string"}, "etag": {"description": "Output only. This checksum is computed by the server based on the value of other fields, and might be sent only on update and delete requests to ensure that the client has an up-to-date value before proceeding.", "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Resource labels.", "type": "object"}, "loggingConfig": {"$ref": "LoggingConfig", "description": "Optional. Config to control Platform logging for the Message Bus. This log configuration is applied to the Message Bus itself, and all the Enrollments attached to it."}, "name": {"description": "Identifier. Resource name of the form projects/{project}/locations/{location}/messageBuses/{message_bus}", "type": "string"}, "uid": {"description": "Output only. Server assigned unique identifier for the channel. The value is a UUID4 string and guaranteed to remain unchanged until the resource is deleted.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The last-modified time.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "NetworkConfig": {"description": "Network Configuration that can be inherited by other protos.", "id": "NetworkConfig", "properties": {"networkAttachment": {"description": "Required. Name of the NetworkAttachment that allows access to the customer's VPC. Format: `projects/{PROJECT_ID}/regions/{REGION}/networkAttachments/{NETWORK_ATTACHMENT_NAME}`", "type": "string"}}, "type": "object"}, "OperationMetadata": {"description": "Represents the metadata of the long-running operation.", "id": "OperationMetadata", "properties": {"apiVersion": {"description": "Output only. API version used to start the operation.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "requestedCancellation": {"description": "Output only. Identifies whether the user has requested cancellation of the operation. Operations that have successfully been cancelled have Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.", "readOnly": true, "type": "boolean"}, "statusMessage": {"description": "Output only. Human-readable status of the operation, if any.", "readOnly": true, "type": "string"}, "target": {"description": "Output only. Server-defined resource path for the target of the operation.", "readOnly": true, "type": "string"}, "verb": {"description": "Output only. Name of the verb executed by the operation.", "readOnly": true, "type": "string"}}, "type": "object"}, "Pipeline": {"description": "A representation of the Pipeline resource.", "id": "Pipeline", "properties": {"annotations": {"additionalProperties": {"type": "string"}, "description": "Optional. User-defined annotations. See https://google.aip.dev/128#annotations.", "type": "object"}, "createTime": {"description": "Output only. The creation time. A timestamp in RFC3339 UTC \"Zulu\" format, with nanosecond resolution and up to nine fractional digits. Examples: \"2014-10-02T15:01:23Z\" and \"2014-10-02T15:01:23.045123456Z\".", "format": "google-datetime", "readOnly": true, "type": "string"}, "cryptoKeyName": {"description": "Optional. Resource name of a KMS crypto key (managed by the user) used to encrypt/decrypt the event data. If not set, an internal Google-owned key will be used to encrypt messages. It must match the pattern \"projects/{project}/locations/{location}/keyRings/{keyring}/cryptoKeys/{key}\".", "type": "string"}, "destinations": {"description": "Required. List of destinations to which messages will be forwarded. Currently, exactly one destination is supported per Pipeline.", "items": {"$ref": "GoogleCloudEventarcV1PipelineDestination"}, "type": "array"}, "displayName": {"description": "Optional. Display name of resource.", "type": "string"}, "etag": {"description": "Output only. This checksum is computed by the server based on the value of other fields, and might be sent only on create requests to ensure that the client has an up-to-date value before proceeding.", "readOnly": true, "type": "string"}, "inputPayloadFormat": {"$ref": "GoogleCloudEventarcV1PipelineMessagePayloadFormat", "description": "Optional. The payload format expected for the messages received by the Pipeline. If input_payload_format is set then any messages not matching this format will be treated as persistent errors. If input_payload_format is not set, then the message data will be treated as an opaque binary and no output format can be set on the Pipeline through the Pipeline.Destination.output_payload_format field. Any Mediations on the Pipeline that involve access to the data field will fail as persistent errors."}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. User labels attached to the Pipeline that can be used to group resources. An object containing a list of \"key\": value pairs. Example: { \"name\": \"wrench\", \"mass\": \"1.3kg\", \"count\": \"3\" }.", "type": "object"}, "loggingConfig": {"$ref": "LoggingConfig", "description": "Optional. Config to control Platform Logging for Pipelines."}, "mediations": {"description": "Optional. List of mediation operations to be performed on the message. Currently, only one Transformation operation is allowed in each Pipeline.", "items": {"$ref": "GoogleCloudEventarcV1PipelineMediation"}, "type": "array"}, "name": {"description": "Identifier. The resource name of the Pipeline. Must be unique within the location of the project and must be in `projects/{project}/locations/{location}/pipelines/{pipeline}` format.", "type": "string"}, "retryPolicy": {"$ref": "GoogleCloudEventarcV1PipelineRetryPolicy", "description": "Optional. The retry policy to use in the pipeline."}, "uid": {"description": "Output only. Server-assigned unique identifier for the Pipeline. The value is a UUID4 string and guaranteed to remain unchanged until the resource is deleted.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The last-modified time. A timestamp in RFC3339 UTC \"Zulu\" format, with nanosecond resolution and up to nine fractional digits. Examples: \"2014-10-02T15:01:23Z\" and \"2014-10-02T15:01:23.045123456Z\".", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "Policy": {"description": "An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { \"bindings\": [ { \"role\": \"roles/resourcemanager.organizationAdmin\", \"members\": [ \"user:<EMAIL>\", \"group:<EMAIL>\", \"domain:google.com\", \"serviceAccount:<EMAIL>\" ] }, { \"role\": \"roles/resourcemanager.organizationViewer\", \"members\": [ \"user:<EMAIL>\" ], \"condition\": { \"title\": \"expirable access\", \"description\": \"Does not grant access after Sep 2020\", \"expression\": \"request.time < timestamp('2020-10-01T00:00:00.000Z')\", } } ], \"etag\": \"BwWWja0YfJA=\", \"version\": 3 } ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).", "id": "Policy", "properties": {"auditConfigs": {"description": "Specifies cloud audit logging configuration for this policy.", "items": {"$ref": "AuditConfig"}, "type": "array"}, "bindings": {"description": "Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.", "items": {"$ref": "Binding"}, "type": "array"}, "etag": {"description": "`etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.", "format": "byte", "type": "string"}, "version": {"description": "Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "type": "integer"}}, "type": "object"}, "Provider": {"description": "A representation of the Provider resource.", "id": "Provider", "properties": {"displayName": {"description": "Output only. Human friendly name for the Provider. For example \"Cloud Storage\".", "readOnly": true, "type": "string"}, "eventTypes": {"description": "Output only. Event types for this provider.", "items": {"$ref": "EventType"}, "readOnly": true, "type": "array"}, "name": {"description": "Output only. In `projects/{project}/locations/{location}/providers/{provider_id}` format.", "readOnly": true, "type": "string"}}, "type": "object"}, "Pubsub": {"description": "Represents a Pub/Sub transport.", "id": "Pubsub", "properties": {"subscription": {"description": "Output only. The name of the Pub/Sub subscription created and managed by Eventarc as a transport for the event delivery. Format: `projects/{PROJECT_ID}/subscriptions/{SUBSCRIPTION_NAME}`.", "readOnly": true, "type": "string"}, "topic": {"description": "Optional. The name of the Pub/Sub topic created and managed by Eventarc as a transport for the event delivery. Format: `projects/{PROJECT_ID}/topics/{TOPIC_NAME}`. You can set an existing topic for triggers of the type `google.cloud.pubsub.topic.v1.messagePublished`. The topic you provide here is not deleted by Eventarc at trigger deletion.", "type": "string"}}, "type": "object"}, "SetIamPolicyRequest": {"description": "Request message for `SetIamPolicy` method.", "id": "SetIamPolicyRequest", "properties": {"policy": {"$ref": "Policy", "description": "REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them."}, "updateMask": {"description": "OPTIONAL: A FieldMask specifying which fields of the policy to modify. Only the fields in the mask will be modified. If no mask is provided, the following default mask is used: `paths: \"bindings, etag\"`", "format": "google-fieldmask", "type": "string"}}, "type": "object"}, "StateCondition": {"description": "A condition that is part of the trigger state computation.", "id": "StateCondition", "properties": {"code": {"description": "The canonical code of the condition.", "enum": ["OK", "CANCELLED", "UNKNOWN", "INVALID_ARGUMENT", "DEADLINE_EXCEEDED", "NOT_FOUND", "ALREADY_EXISTS", "PERMISSION_DENIED", "UNAUTHENTICATED", "RESOURCE_EXHAUSTED", "FAILED_PRECONDITION", "ABORTED", "OUT_OF_RANGE", "UNIMPLEMENTED", "INTERNAL", "UNAVAILABLE", "DATA_LOSS"], "enumDescriptions": ["Not an error; returned on success. HTTP Mapping: 200 OK", "The operation was cancelled, typically by the caller. HTTP Mapping: 499 Client Closed Request", "Unknown error. For example, this error may be returned when a `Status` value received from another address space belongs to an error space that is not known in this address space. Also errors raised by APIs that do not return enough error information may be converted to this error. HTTP Mapping: 500 Internal Server Error", "The client specified an invalid argument. Note that this differs from `FAILED_PRECONDITION`. `INVALID_ARGUMENT` indicates arguments that are problematic regardless of the state of the system (e.g., a malformed file name). HTTP Mapping: 400 Bad Request", "The deadline expired before the operation could complete. For operations that change the state of the system, this error may be returned even if the operation has completed successfully. For example, a successful response from a server could have been delayed long enough for the deadline to expire. HTTP Mapping: 504 Gateway Timeout", "Some requested entity (e.g., file or directory) was not found. Note to server developers: if a request is denied for an entire class of users, such as gradual feature rollout or undocumented allowlist, `NOT_FOUND` may be used. If a request is denied for some users within a class of users, such as user-based access control, `PERMISSION_DENIED` must be used. HTTP Mapping: 404 Not Found", "The entity that a client attempted to create (e.g., file or directory) already exists. HTTP Mapping: 409 Conflict", "The caller does not have permission to execute the specified operation. `PERMISSION_DENIED` must not be used for rejections caused by exhausting some resource (use `RESOURCE_EXHAUSTED` instead for those errors). `PERMISSION_DENIED` must not be used if the caller can not be identified (use `UNAUTHENTICATED` instead for those errors). This error code does not imply the request is valid or the requested entity exists or satisfies other pre-conditions. HTTP Mapping: 403 Forbidden", "The request does not have valid authentication credentials for the operation. HTTP Mapping: 401 Unauthorized", "Some resource has been exhausted, perhaps a per-user quota, or perhaps the entire file system is out of space. HTTP Mapping: 429 Too Many Requests", "The operation was rejected because the system is not in a state required for the operation's execution. For example, the directory to be deleted is non-empty, an rmdir operation is applied to a non-directory, etc. Service implementors can use the following guidelines to decide between `FAILED_PRECONDITION`, `ABORTED`, and `<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>`: (a) Use `<PERSON><PERSON><PERSON><PERSON>ABLE` if the client can retry just the failing call. (b) Use `ABORTED` if the client should retry at a higher level. For example, when a client-specified test-and-set fails, indicating the client should restart a read-modify-write sequence. (c) Use `FAILED_PRECONDITION` if the client should not retry until the system state has been explicitly fixed. For example, if an \"rmdir\" fails because the directory is non-empty, `FAILED_PRECONDITION` should be returned since the client should not retry unless the files are deleted from the directory. HTTP Mapping: 400 Bad Request", "The operation was aborted, typically due to a concurrency issue such as a sequencer check failure or transaction abort. See the guidelines above for deciding between `FAILED_PRECONDITION`, `ABORTED`, and `<PERSON>AVAILABLE`. HTTP Mapping: 409 Conflict", "The operation was attempted past the valid range. E.g., seeking or reading past end-of-file. Unlike `INVALID_ARGUMENT`, this error indicates a problem that may be fixed if the system state changes. For example, a 32-bit file system will generate `INVALID_ARGUMENT` if asked to read at an offset that is not in the range [0,2^32-1], but it will generate `OUT_OF_RANGE` if asked to read from an offset past the current file size. There is a fair bit of overlap between `FAILED_PRECONDITION` and `OUT_OF_RANGE`. We recommend using `OUT_OF_RANGE` (the more specific error) when it applies so that callers who are iterating through a space can easily look for an `OUT_OF_RANGE` error to detect when they are done. HTTP Mapping: 400 Bad Request", "The operation is not implemented or is not supported/enabled in this service. HTTP Mapping: 501 Not Implemented", "Internal errors. This means that some invariants expected by the underlying system have been broken. This error code is reserved for serious errors. HTTP Mapping: 500 Internal Server Error", "The service is currently unavailable. This is most likely a transient condition, which can be corrected by retrying with a backoff. Note that it is not always safe to retry non-idempotent operations. See the guidelines above for deciding between `FAILED_PRECONDITION`, `ABORTED`, and `<PERSON><PERSON><PERSON><PERSON>ABLE`. HTTP Mapping: 503 Service Unavailable", "Unrecoverable data loss or corruption. HTTP Mapping: 500 Internal Server Error"], "type": "string"}, "message": {"description": "Human-readable message.", "type": "string"}}, "type": "object"}, "TestIamPermissionsRequest": {"description": "Request message for `TestIamPermissions` method.", "id": "TestIamPermissionsRequest", "properties": {"permissions": {"description": "The set of permissions to check for the `resource`. Permissions with wildcards (such as `*` or `storage.*`) are not allowed. For more information see [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions).", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "TestIamPermissionsResponse": {"description": "Response message for `TestIamPermissions` method.", "id": "TestIamPermissionsResponse", "properties": {"permissions": {"description": "A subset of `TestPermissionsRequest.permissions` that the caller is allowed.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "Transport": {"description": "Represents the transport intermediaries created for the trigger to deliver events.", "id": "Transport", "properties": {"pubsub": {"$ref": "Pubsub", "description": "The Pub/Sub topic and subscription used by Eventarc as a transport intermediary."}}, "type": "object"}, "Trigger": {"description": "A representation of the trigger resource.", "id": "<PERSON><PERSON>", "properties": {"channel": {"description": "Optional. The name of the channel associated with the trigger in `projects/{project}/locations/{location}/channels/{channel}` format. You must provide a channel to receive events from Eventarc SaaS partners.", "type": "string"}, "conditions": {"additionalProperties": {"$ref": "StateCondition"}, "description": "Output only. The reason(s) why a trigger is in FAILED state.", "readOnly": true, "type": "object"}, "createTime": {"description": "Output only. The creation time.", "format": "google-datetime", "readOnly": true, "type": "string"}, "destination": {"$ref": "Destination", "description": "Required. Destina<PERSON> specifies where the events should be sent to."}, "etag": {"description": "Output only. This checksum is computed by the server based on the value of other fields, and might be sent only on create requests to ensure that the client has an up-to-date value before proceeding.", "readOnly": true, "type": "string"}, "eventDataContentType": {"description": "Optional. EventDataContentType specifies the type of payload in MIME format that is expected from the CloudEvent data field. This is set to `application/json` if the value is not defined.", "type": "string"}, "eventFilters": {"description": "Required. Unordered list. The list of filters that applies to event attributes. Only events that match all the provided filters are sent to the destination.", "items": {"$ref": "EventFilter"}, "type": "array"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. User labels attached to the triggers that can be used to group resources.", "type": "object"}, "name": {"description": "Required. The resource name of the trigger. Must be unique within the location of the project and must be in `projects/{project}/locations/{location}/triggers/{trigger}` format.", "type": "string"}, "satisfiesPzs": {"description": "Output only. Whether or not this Trigger satisfies the requirements of physical zone separation", "readOnly": true, "type": "boolean"}, "serviceAccount": {"description": "Optional. The IAM service account email associated with the trigger. The service account represents the identity of the trigger. The `iam.serviceAccounts.actAs` permission must be granted on the service account to allow a principal to impersonate the service account. For more information, see the [Roles and permissions](/eventarc/docs/all-roles-permissions) page specific to the trigger destination.", "type": "string"}, "transport": {"$ref": "Transport", "description": "Optional. To deliver messages, Eventarc might use other Google Cloud products as a transport intermediary. This field contains a reference to that transport intermediary. This information can be used for debugging purposes."}, "uid": {"description": "Output only. Server-assigned unique identifier for the trigger. The value is a UUID4 string and guaranteed to remain unchanged until the resource is deleted.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The last-modified time.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Eventarc API", "version": "v1", "version_module": true}