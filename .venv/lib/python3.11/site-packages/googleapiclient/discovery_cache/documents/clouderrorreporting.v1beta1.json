{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://clouderrorreporting.googleapis.com/", "batchPath": "batch", "canonicalName": "Clouderrorreporting", "description": "Groups and counts similar errors from cloud services and applications, reports new errors, and provides access to error groups and their associated errors. ", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/error-reporting/", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "clouderrorreporting:v1beta1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://clouderrorreporting.mtls.googleapis.com/", "name": "clouderrorreporting", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"methods": {"deleteEvents": {"description": "Deletes all error events of a given project.", "flatPath": "v1beta1/projects/{projectsId}/events", "httpMethod": "DELETE", "id": "clouderrorreporting.projects.deleteEvents", "parameterOrder": ["projectName"], "parameters": {"projectName": {"description": "Required. The resource name of the Google Cloud Platform project. Written as `projects/{projectID}` or `projects/{projectID}/locations/{location}`, where `{projectID}` is the [Google Cloud Platform project ID](https://support.google.com/cloud/answer/6158840) and `{location}` is a Cloud region. Examples: `projects/my-project-123`, `projects/my-project-123/locations/global`. For a list of supported locations, see [Supported Regions](https://cloud.google.com/logging/docs/region-support). `global` is the default when unspecified.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+projectName}/events", "response": {"$ref": "DeleteEventsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"events": {"methods": {"list": {"description": "Lists the specified events.", "flatPath": "v1beta1/projects/{projectsId}/events", "httpMethod": "GET", "id": "clouderrorreporting.projects.events.list", "parameterOrder": ["projectName"], "parameters": {"groupId": {"description": "Required. The group for which events shall be returned. The `group_id` is a unique identifier for a particular error group. The identifier is derived from key parts of the error-log content and is treated as Service Data. For information about how Service Data is handled, see [Google Cloud Privacy Notice](https://cloud.google.com/terms/cloud-privacy-notice).", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of results to return per response.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A `next_page_token` provided by a previous response.", "location": "query", "type": "string"}, "projectName": {"description": "Required. The resource name of the Google Cloud Platform project. Written as `projects/{projectID}` or `projects/{projectID}/locations/{location}`, where `{projectID}` is the [Google Cloud Platform project ID](https://support.google.com/cloud/answer/6158840) and `{location}` is a Cloud region. Examples: `projects/my-project-123`, `projects/my-project-123/locations/global`. For a list of supported locations, see [Supported Regions](https://cloud.google.com/logging/docs/region-support). `global` is the default when unspecified.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}, "serviceFilter.resourceType": {"description": "Optional. The exact value to match against [`ServiceContext.resource_type`](/error-reporting/reference/rest/v1beta1/ServiceContext#FIELDS.resource_type).", "location": "query", "type": "string"}, "serviceFilter.service": {"description": "Optional. The exact value to match against [`ServiceContext.service`](/error-reporting/reference/rest/v1beta1/ServiceContext#FIELDS.service).", "location": "query", "type": "string"}, "serviceFilter.version": {"description": "Optional. The exact value to match against [`ServiceContext.version`](/error-reporting/reference/rest/v1beta1/ServiceContext#FIELDS.version).", "location": "query", "type": "string"}, "timeRange.period": {"description": "Restricts the query to the specified time range.", "enum": ["PERIOD_UNSPECIFIED", "PERIOD_1_HOUR", "PERIOD_6_HOURS", "PERIOD_1_DAY", "PERIOD_1_WEEK", "PERIOD_30_DAYS"], "enumDescriptions": ["Do not use.", "Retrieve data for the last hour. Recommended minimum timed count duration: 1 min.", "Retrieve data for the last 6 hours. Recommended minimum timed count duration: 10 min.", "Retrieve data for the last day. Recommended minimum timed count duration: 1 hour.", "Retrieve data for the last week. Recommended minimum timed count duration: 6 hours.", "Retrieve data for the last 30 days. Recommended minimum timed count duration: 1 day."], "location": "query", "type": "string"}}, "path": "v1beta1/{+projectName}/events", "response": {"$ref": "ListEventsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "report": {"description": "Report an individual error event and record the event to a log. This endpoint accepts **either** an OAuth token, **or** an [API key](https://support.google.com/cloud/answer/6158862) for authentication. To use an API key, append it to the URL as the value of a `key` parameter. For example: `POST https://clouderrorreporting.googleapis.com/v1beta1/{projectName}/events:report?key=123ABC456` **Note:** [Error Reporting] (https://cloud.google.com/error-reporting) is a service built on Cloud Logging and can analyze log entries when all of the following are true: * Customer-managed encryption keys (CMEK) are disabled on the log bucket. * The log bucket satisfies one of the following: * The log bucket is stored in the same project where the logs originated. * The logs were routed to a project, and then that project stored those logs in a log bucket that it owns.", "flatPath": "v1beta1/projects/{projectsId}/events:report", "httpMethod": "POST", "id": "clouderrorreporting.projects.events.report", "parameterOrder": ["projectName"], "parameters": {"projectName": {"description": "Required. The resource name of the Google Cloud Platform project. Written as `projects/{projectId}`, where `{projectId}` is the [Google Cloud Platform project ID](https://support.google.com/cloud/answer/6158840). Example: // `projects/my-project-123`.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+projectName}/events:report", "request": {"$ref": "ReportedErrorEvent"}, "response": {"$ref": "ReportErrorEventResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "groupStats": {"methods": {"list": {"description": "Lists the specified groups.", "flatPath": "v1beta1/projects/{projectsId}/groupStats", "httpMethod": "GET", "id": "clouderrorreporting.projects.groupStats.list", "parameterOrder": ["projectName"], "parameters": {"alignment": {"description": "Optional. The alignment of the timed counts to be returned. Default is `ALIGNMENT_EQUAL_AT_END`.", "enum": ["ERROR_COUNT_ALIGNMENT_UNSPECIFIED", "ALIGNMENT_EQUAL_ROUNDED", "ALIGNMENT_EQUAL_AT_END"], "enumDescriptions": ["No alignment specified.", "The time periods shall be consecutive, have width equal to the requested duration, and be aligned at the alignment_time provided in the request. The alignment_time does not have to be inside the query period but even if it is outside, only time periods are returned which overlap with the query period. A rounded alignment will typically result in a different size of the first or the last time period.", "The time periods shall be consecutive, have width equal to the requested duration, and be aligned at the end of the requested time period. This can result in a different size of the first time period."], "location": "query", "type": "string"}, "alignmentTime": {"description": "Optional. Time where the timed counts shall be aligned if rounded alignment is chosen. Default is 00:00 UTC.", "format": "google-datetime", "location": "query", "type": "string"}, "groupId": {"description": "Optional. List all ErrorGroupStats with these IDs. The `group_id` is a unique identifier for a particular error group. The identifier is derived from key parts of the error-log content and is treated as Service Data. For information about how Service Data is handled, see [Google Cloud Privacy Notice] (https://cloud.google.com/terms/cloud-privacy-notice).", "location": "query", "repeated": true, "type": "string"}, "order": {"description": "Optional. The sort order in which the results are returned. Default is `COUNT_DESC`.", "enum": ["GROUP_ORDER_UNSPECIFIED", "COUNT_DESC", "LAST_SEEN_DESC", "CREATED_DESC", "AFFECTED_USERS_DESC"], "enumDescriptions": ["No group order specified.", "Total count of errors in the given time window in descending order.", "Timestamp when the group was last seen in the given time window in descending order.", "Timestamp when the group was created in descending order.", "Number of affected users in the given time window in descending order."], "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of results to return per response. Default is 20.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A next_page_token provided by a previous response. To view additional results, pass this token along with the identical query parameters as the first request.", "location": "query", "type": "string"}, "projectName": {"description": "Required. The resource name of the Google Cloud Platform project. Written as `projects/{projectID}` or `projects/{projectNumber}`, where `{projectID}` and `{projectNumber}` can be found in the [Google Cloud console](https://support.google.com/cloud/answer/6158840). It may also include a location, such as `projects/{projectID}/locations/{location}` where `{location}` is a cloud region. Examples: `projects/my-project-123`, `projects/5551234`, `projects/my-project-123/locations/us-central1`, `projects/5551234/locations/us-central1`. For a list of supported locations, see [Supported Regions](https://cloud.google.com/logging/docs/region-support). `global` is the default when unspecified. Use `-` as a wildcard to request group stats from all regions.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}, "serviceFilter.resourceType": {"description": "Optional. The exact value to match against [`ServiceContext.resource_type`](/error-reporting/reference/rest/v1beta1/ServiceContext#FIELDS.resource_type).", "location": "query", "type": "string"}, "serviceFilter.service": {"description": "Optional. The exact value to match against [`ServiceContext.service`](/error-reporting/reference/rest/v1beta1/ServiceContext#FIELDS.service).", "location": "query", "type": "string"}, "serviceFilter.version": {"description": "Optional. The exact value to match against [`ServiceContext.version`](/error-reporting/reference/rest/v1beta1/ServiceContext#FIELDS.version).", "location": "query", "type": "string"}, "timeRange.period": {"description": "Restricts the query to the specified time range.", "enum": ["PERIOD_UNSPECIFIED", "PERIOD_1_HOUR", "PERIOD_6_HOURS", "PERIOD_1_DAY", "PERIOD_1_WEEK", "PERIOD_30_DAYS"], "enumDescriptions": ["Do not use.", "Retrieve data for the last hour. Recommended minimum timed count duration: 1 min.", "Retrieve data for the last 6 hours. Recommended minimum timed count duration: 10 min.", "Retrieve data for the last day. Recommended minimum timed count duration: 1 hour.", "Retrieve data for the last week. Recommended minimum timed count duration: 6 hours.", "Retrieve data for the last 30 days. Recommended minimum timed count duration: 1 day."], "location": "query", "type": "string"}, "timedCountDuration": {"description": "Optional. The preferred duration for a single returned TimedCount. If not set, no timed counts are returned.", "format": "google-duration", "location": "query", "type": "string"}}, "path": "v1beta1/{+projectName}/groupStats", "response": {"$ref": "ListGroupStatsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "groups": {"methods": {"get": {"description": "Get the specified group.", "flatPath": "v1beta1/projects/{projectsId}/groups/{groupsId}", "httpMethod": "GET", "id": "clouderrorreporting.projects.groups.get", "parameterOrder": ["groupName"], "parameters": {"groupName": {"description": "Required. The group resource name. Written as either `projects/{projectID}/groups/{group_id}` or `projects/{projectID}/locations/{location}/groups/{group_id}`. Call groupStats.list to return a list of groups belonging to this project. Examples: `projects/my-project-123/groups/my-group`, `projects/my-project-123/locations/global/groups/my-group` In the group resource name, the `group_id` is a unique identifier for a particular error group. The identifier is derived from key parts of the error-log content and is treated as Service Data. For information about how Service Data is handled, see [Google Cloud Privacy Notice](https://cloud.google.com/terms/cloud-privacy-notice). For a list of supported locations, see [Supported Regions](https://cloud.google.com/logging/docs/region-support). `global` is the default when unspecified.", "location": "path", "pattern": "^projects/[^/]+/groups/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+groupName}", "response": {"$ref": "ErrorGroup"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "update": {"description": "Replace the data for the specified group. Fails if the group does not exist.", "flatPath": "v1beta1/projects/{projectsId}/groups/{groupsId}", "httpMethod": "PUT", "id": "clouderrorreporting.projects.groups.update", "parameterOrder": ["name"], "parameters": {"name": {"description": "The group resource name. Written as `projects/{projectID}/groups/{group_id}` or `projects/{projectID}/locations/{location}/groups/{group_id}` Examples: `projects/my-project-123/groups/my-group`, `projects/my-project-123/locations/us-central1/groups/my-group` In the group resource name, the `group_id` is a unique identifier for a particular error group. The identifier is derived from key parts of the error-log content and is treated as Service Data. For information about how Service Data is handled, see [Google Cloud Privacy Notice](https://cloud.google.com/terms/cloud-privacy-notice). For a list of supported locations, see [Supported Regions](https://cloud.google.com/logging/docs/region-support). `global` is the default when unspecified.", "location": "path", "pattern": "^projects/[^/]+/groups/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "ErrorGroup"}, "response": {"$ref": "ErrorGroup"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "locations": {"methods": {"deleteEvents": {"description": "Deletes all error events of a given project.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/events", "httpMethod": "DELETE", "id": "clouderrorreporting.projects.locations.deleteEvents", "parameterOrder": ["projectName"], "parameters": {"projectName": {"description": "Required. The resource name of the Google Cloud Platform project. Written as `projects/{projectID}` or `projects/{projectID}/locations/{location}`, where `{projectID}` is the [Google Cloud Platform project ID](https://support.google.com/cloud/answer/6158840) and `{location}` is a Cloud region. Examples: `projects/my-project-123`, `projects/my-project-123/locations/global`. For a list of supported locations, see [Supported Regions](https://cloud.google.com/logging/docs/region-support). `global` is the default when unspecified.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+projectName}/events", "response": {"$ref": "DeleteEventsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"events": {"methods": {"list": {"description": "Lists the specified events.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/events", "httpMethod": "GET", "id": "clouderrorreporting.projects.locations.events.list", "parameterOrder": ["projectName"], "parameters": {"groupId": {"description": "Required. The group for which events shall be returned. The `group_id` is a unique identifier for a particular error group. The identifier is derived from key parts of the error-log content and is treated as Service Data. For information about how Service Data is handled, see [Google Cloud Privacy Notice](https://cloud.google.com/terms/cloud-privacy-notice).", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of results to return per response.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A `next_page_token` provided by a previous response.", "location": "query", "type": "string"}, "projectName": {"description": "Required. The resource name of the Google Cloud Platform project. Written as `projects/{projectID}` or `projects/{projectID}/locations/{location}`, where `{projectID}` is the [Google Cloud Platform project ID](https://support.google.com/cloud/answer/6158840) and `{location}` is a Cloud region. Examples: `projects/my-project-123`, `projects/my-project-123/locations/global`. For a list of supported locations, see [Supported Regions](https://cloud.google.com/logging/docs/region-support). `global` is the default when unspecified.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "serviceFilter.resourceType": {"description": "Optional. The exact value to match against [`ServiceContext.resource_type`](/error-reporting/reference/rest/v1beta1/ServiceContext#FIELDS.resource_type).", "location": "query", "type": "string"}, "serviceFilter.service": {"description": "Optional. The exact value to match against [`ServiceContext.service`](/error-reporting/reference/rest/v1beta1/ServiceContext#FIELDS.service).", "location": "query", "type": "string"}, "serviceFilter.version": {"description": "Optional. The exact value to match against [`ServiceContext.version`](/error-reporting/reference/rest/v1beta1/ServiceContext#FIELDS.version).", "location": "query", "type": "string"}, "timeRange.period": {"description": "Restricts the query to the specified time range.", "enum": ["PERIOD_UNSPECIFIED", "PERIOD_1_HOUR", "PERIOD_6_HOURS", "PERIOD_1_DAY", "PERIOD_1_WEEK", "PERIOD_30_DAYS"], "enumDescriptions": ["Do not use.", "Retrieve data for the last hour. Recommended minimum timed count duration: 1 min.", "Retrieve data for the last 6 hours. Recommended minimum timed count duration: 10 min.", "Retrieve data for the last day. Recommended minimum timed count duration: 1 hour.", "Retrieve data for the last week. Recommended minimum timed count duration: 6 hours.", "Retrieve data for the last 30 days. Recommended minimum timed count duration: 1 day."], "location": "query", "type": "string"}}, "path": "v1beta1/{+projectName}/events", "response": {"$ref": "ListEventsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "groupStats": {"methods": {"list": {"description": "Lists the specified groups.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/groupStats", "httpMethod": "GET", "id": "clouderrorreporting.projects.locations.groupStats.list", "parameterOrder": ["projectName"], "parameters": {"alignment": {"description": "Optional. The alignment of the timed counts to be returned. Default is `ALIGNMENT_EQUAL_AT_END`.", "enum": ["ERROR_COUNT_ALIGNMENT_UNSPECIFIED", "ALIGNMENT_EQUAL_ROUNDED", "ALIGNMENT_EQUAL_AT_END"], "enumDescriptions": ["No alignment specified.", "The time periods shall be consecutive, have width equal to the requested duration, and be aligned at the alignment_time provided in the request. The alignment_time does not have to be inside the query period but even if it is outside, only time periods are returned which overlap with the query period. A rounded alignment will typically result in a different size of the first or the last time period.", "The time periods shall be consecutive, have width equal to the requested duration, and be aligned at the end of the requested time period. This can result in a different size of the first time period."], "location": "query", "type": "string"}, "alignmentTime": {"description": "Optional. Time where the timed counts shall be aligned if rounded alignment is chosen. Default is 00:00 UTC.", "format": "google-datetime", "location": "query", "type": "string"}, "groupId": {"description": "Optional. List all ErrorGroupStats with these IDs. The `group_id` is a unique identifier for a particular error group. The identifier is derived from key parts of the error-log content and is treated as Service Data. For information about how Service Data is handled, see [Google Cloud Privacy Notice] (https://cloud.google.com/terms/cloud-privacy-notice).", "location": "query", "repeated": true, "type": "string"}, "order": {"description": "Optional. The sort order in which the results are returned. Default is `COUNT_DESC`.", "enum": ["GROUP_ORDER_UNSPECIFIED", "COUNT_DESC", "LAST_SEEN_DESC", "CREATED_DESC", "AFFECTED_USERS_DESC"], "enumDescriptions": ["No group order specified.", "Total count of errors in the given time window in descending order.", "Timestamp when the group was last seen in the given time window in descending order.", "Timestamp when the group was created in descending order.", "Number of affected users in the given time window in descending order."], "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of results to return per response. Default is 20.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A next_page_token provided by a previous response. To view additional results, pass this token along with the identical query parameters as the first request.", "location": "query", "type": "string"}, "projectName": {"description": "Required. The resource name of the Google Cloud Platform project. Written as `projects/{projectID}` or `projects/{projectNumber}`, where `{projectID}` and `{projectNumber}` can be found in the [Google Cloud console](https://support.google.com/cloud/answer/6158840). It may also include a location, such as `projects/{projectID}/locations/{location}` where `{location}` is a cloud region. Examples: `projects/my-project-123`, `projects/5551234`, `projects/my-project-123/locations/us-central1`, `projects/5551234/locations/us-central1`. For a list of supported locations, see [Supported Regions](https://cloud.google.com/logging/docs/region-support). `global` is the default when unspecified. Use `-` as a wildcard to request group stats from all regions.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "serviceFilter.resourceType": {"description": "Optional. The exact value to match against [`ServiceContext.resource_type`](/error-reporting/reference/rest/v1beta1/ServiceContext#FIELDS.resource_type).", "location": "query", "type": "string"}, "serviceFilter.service": {"description": "Optional. The exact value to match against [`ServiceContext.service`](/error-reporting/reference/rest/v1beta1/ServiceContext#FIELDS.service).", "location": "query", "type": "string"}, "serviceFilter.version": {"description": "Optional. The exact value to match against [`ServiceContext.version`](/error-reporting/reference/rest/v1beta1/ServiceContext#FIELDS.version).", "location": "query", "type": "string"}, "timeRange.period": {"description": "Restricts the query to the specified time range.", "enum": ["PERIOD_UNSPECIFIED", "PERIOD_1_HOUR", "PERIOD_6_HOURS", "PERIOD_1_DAY", "PERIOD_1_WEEK", "PERIOD_30_DAYS"], "enumDescriptions": ["Do not use.", "Retrieve data for the last hour. Recommended minimum timed count duration: 1 min.", "Retrieve data for the last 6 hours. Recommended minimum timed count duration: 10 min.", "Retrieve data for the last day. Recommended minimum timed count duration: 1 hour.", "Retrieve data for the last week. Recommended minimum timed count duration: 6 hours.", "Retrieve data for the last 30 days. Recommended minimum timed count duration: 1 day."], "location": "query", "type": "string"}, "timedCountDuration": {"description": "Optional. The preferred duration for a single returned TimedCount. If not set, no timed counts are returned.", "format": "google-duration", "location": "query", "type": "string"}}, "path": "v1beta1/{+projectName}/groupStats", "response": {"$ref": "ListGroupStatsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "groups": {"methods": {"get": {"description": "Get the specified group.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/groups/{groupsId}", "httpMethod": "GET", "id": "clouderrorreporting.projects.locations.groups.get", "parameterOrder": ["groupName"], "parameters": {"groupName": {"description": "Required. The group resource name. Written as either `projects/{projectID}/groups/{group_id}` or `projects/{projectID}/locations/{location}/groups/{group_id}`. Call groupStats.list to return a list of groups belonging to this project. Examples: `projects/my-project-123/groups/my-group`, `projects/my-project-123/locations/global/groups/my-group` In the group resource name, the `group_id` is a unique identifier for a particular error group. The identifier is derived from key parts of the error-log content and is treated as Service Data. For information about how Service Data is handled, see [Google Cloud Privacy Notice](https://cloud.google.com/terms/cloud-privacy-notice). For a list of supported locations, see [Supported Regions](https://cloud.google.com/logging/docs/region-support). `global` is the default when unspecified.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/groups/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+groupName}", "response": {"$ref": "ErrorGroup"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "update": {"description": "Replace the data for the specified group. Fails if the group does not exist.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/groups/{groupsId}", "httpMethod": "PUT", "id": "clouderrorreporting.projects.locations.groups.update", "parameterOrder": ["name"], "parameters": {"name": {"description": "The group resource name. Written as `projects/{projectID}/groups/{group_id}` or `projects/{projectID}/locations/{location}/groups/{group_id}` Examples: `projects/my-project-123/groups/my-group`, `projects/my-project-123/locations/us-central1/groups/my-group` In the group resource name, the `group_id` is a unique identifier for a particular error group. The identifier is derived from key parts of the error-log content and is treated as Service Data. For information about how Service Data is handled, see [Google Cloud Privacy Notice](https://cloud.google.com/terms/cloud-privacy-notice). For a list of supported locations, see [Supported Regions](https://cloud.google.com/logging/docs/region-support). `global` is the default when unspecified.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/groups/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "ErrorGroup"}, "response": {"$ref": "ErrorGroup"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}, "revision": "20240705", "rootUrl": "https://clouderrorreporting.googleapis.com/", "schemas": {"DeleteEventsResponse": {"description": "Response message for deleting error events.", "id": "DeleteEventsResponse", "properties": {}, "type": "object"}, "ErrorContext": {"description": "A description of the context in which an error occurred. This data should be provided by the application when reporting an error, unless the error report has been generated automatically from Google App Engine logs.", "id": "ErrorContext", "properties": {"httpRequest": {"$ref": "HttpRequestContext", "description": "The HTTP request which was processed when the error was triggered."}, "reportLocation": {"$ref": "SourceLocation", "description": "The location in the source code where the decision was made to report the error, usually the place where it was logged. For a logged exception this would be the source line where the exception is logged, usually close to the place where it was caught."}, "sourceReferences": {"description": "Source code that was used to build the executable which has caused the given error message.", "items": {"$ref": "SourceReference"}, "type": "array"}, "user": {"description": "The user who caused or was affected by the crash. This can be a user ID, an email address, or an arbitrary token that uniquely identifies the user. When sending an error report, leave this field empty if the user was not logged in. In this case the Error Reporting system will use other data, such as remote IP address, to distinguish affected users. See `affected_users_count` in `ErrorGroupStats`.", "type": "string"}}, "type": "object"}, "ErrorEvent": {"description": "An error event which is returned by the Error Reporting system.", "id": "ErrorEvent", "properties": {"context": {"$ref": "ErrorContext", "description": "Data about the context in which the error occurred."}, "eventTime": {"description": "Time when the event occurred as provided in the error report. If the report did not contain a timestamp, the time the error was received by the Error Reporting system is used.", "format": "google-datetime", "type": "string"}, "message": {"description": "The stack trace that was reported or logged by the service.", "type": "string"}, "serviceContext": {"$ref": "ServiceContext", "description": "The `ServiceContext` for which this error was reported."}}, "type": "object"}, "ErrorGroup": {"description": "Description of a group of similar error events.", "id": "ErrorGroup", "properties": {"groupId": {"description": "An opaque identifier of the group. This field is assigned by the Error Reporting system and always populated. In the group resource name, the `group_id` is a unique identifier for a particular error group. The identifier is derived from key parts of the error-log content and is treated as Service Data. For information about how Service Data is handled, see [Google Cloud Privacy Notice](https://cloud.google.com/terms/cloud-privacy-notice).", "type": "string"}, "name": {"description": "The group resource name. Written as `projects/{projectID}/groups/{group_id}` or `projects/{projectID}/locations/{location}/groups/{group_id}` Examples: `projects/my-project-123/groups/my-group`, `projects/my-project-123/locations/us-central1/groups/my-group` In the group resource name, the `group_id` is a unique identifier for a particular error group. The identifier is derived from key parts of the error-log content and is treated as Service Data. For information about how Service Data is handled, see [Google Cloud Privacy Notice](https://cloud.google.com/terms/cloud-privacy-notice). For a list of supported locations, see [Supported Regions](https://cloud.google.com/logging/docs/region-support). `global` is the default when unspecified.", "type": "string"}, "resolutionStatus": {"description": "Error group's resolution status. An unspecified resolution status will be interpreted as OPEN", "enum": ["RESOLUTION_STATUS_UNSPECIFIED", "OPEN", "ACKNOWLEDGED", "RESOLVED", "MUTED"], "enumDescriptions": ["Status is unknown. When left unspecified in requests, it is treated like OPEN.", "The error group is not being addressed. This is the default for new groups. It is also used for errors re-occurring after marked RESOLVED.", "Error Group manually acknowledged, it can have an issue link attached.", "Error Group manually resolved, more events for this group are not expected to occur.", "The error group is muted and excluded by default on group stats requests."], "type": "string"}, "trackingIssues": {"description": "Associated tracking issues.", "items": {"$ref": "TrackingIssue"}, "type": "array"}}, "type": "object"}, "ErrorGroupStats": {"description": "Data extracted for a specific group based on certain filter criteria, such as a given time period and/or service filter.", "id": "ErrorGroupStats", "properties": {"affectedServices": {"description": "Service contexts with a non-zero error count for the given filter criteria. This list can be truncated if multiple services are affected. Refer to `num_affected_services` for the total count.", "items": {"$ref": "ServiceContext"}, "type": "array"}, "affectedUsersCount": {"description": "Approximate number of affected users in the given group that match the filter criteria. Users are distinguished by data in the ErrorContext of the individual error events, such as their login name or their remote IP address in case of HTTP requests. The number of affected users can be zero even if the number of errors is non-zero if no data was provided from which the affected user could be deduced. Users are counted based on data in the request context that was provided in the error report. If more users are implicitly affected, such as due to a crash of the whole service, this is not reflected here.", "format": "int64", "type": "string"}, "count": {"description": "Approximate total number of events in the given group that match the filter criteria.", "format": "int64", "type": "string"}, "firstSeenTime": {"description": "Approximate first occurrence that was ever seen for this group and which matches the given filter criteria, ignoring the time_range that was specified in the request.", "format": "google-datetime", "type": "string"}, "group": {"$ref": "ErrorGroup", "description": "Group data that is independent of the filter criteria."}, "lastSeenTime": {"description": "Approximate last occurrence that was ever seen for this group and which matches the given filter criteria, ignoring the time_range that was specified in the request.", "format": "google-datetime", "type": "string"}, "numAffectedServices": {"description": "The total number of services with a non-zero error count for the given filter criteria.", "format": "int32", "type": "integer"}, "representative": {"$ref": "ErrorEvent", "description": "An arbitrary event that is chosen as representative for the whole group. The representative event is intended to be used as a quick preview for the whole group. Events in the group are usually sufficiently similar to each other such that showing an arbitrary representative provides insight into the characteristics of the group as a whole."}, "timedCounts": {"description": "Approximate number of occurrences over time. Timed counts returned by ListGroups are guaranteed to be: - Inside the requested time interval - Non-overlapping, and - Ordered by ascending time.", "items": {"$ref": "TimedCount"}, "type": "array"}}, "type": "object"}, "HttpRequestContext": {"description": "HTTP request data that is related to a reported error. This data should be provided by the application when reporting an error, unless the error report has been generated automatically from Google App Engine logs.", "id": "HttpRequestContext", "properties": {"method": {"description": "The type of HTTP request, such as `GET`, `POST`, etc.", "type": "string"}, "referrer": {"description": "The referrer information that is provided with the request.", "type": "string"}, "remoteIp": {"description": "The IP address from which the request originated. This can be IPv4, IPv6, or a token which is derived from the IP address, depending on the data that has been provided in the error report.", "type": "string"}, "responseStatusCode": {"description": "The HTTP response status code for the request.", "format": "int32", "type": "integer"}, "url": {"description": "The URL of the request.", "type": "string"}, "userAgent": {"description": "The user agent information that is provided with the request.", "type": "string"}}, "type": "object"}, "ListEventsResponse": {"description": "Contains a set of requested error events.", "id": "ListEventsResponse", "properties": {"errorEvents": {"description": "The error events which match the given request.", "items": {"$ref": "ErrorEvent"}, "type": "array"}, "nextPageToken": {"description": "If non-empty, more results are available. Pass this token, along with the same query parameters as the first request, to view the next page of results.", "type": "string"}, "timeRangeBegin": {"description": "The timestamp specifies the start time to which the request was restricted.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "ListGroupStatsResponse": {"description": "Contains a set of requested error group stats.", "id": "ListGroupStatsResponse", "properties": {"errorGroupStats": {"description": "The error group stats which match the given request.", "items": {"$ref": "ErrorGroupStats"}, "type": "array"}, "nextPageToken": {"description": "If non-empty, more results are available. Pass this token, along with the same query parameters as the first request, to view the next page of results.", "type": "string"}, "timeRangeBegin": {"description": "The timestamp specifies the start time to which the request was restricted. The start time is set based on the requested time range. It may be adjusted to a later time if a project has exceeded the storage quota and older data has been deleted.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "ReportErrorEventResponse": {"description": "Response for reporting an individual error event. Data may be added to this message in the future.", "id": "ReportErrorEventResponse", "properties": {}, "type": "object"}, "ReportedErrorEvent": {"description": "An error event which is reported to the Error Reporting system.", "id": "ReportedErrorEvent", "properties": {"context": {"$ref": "ErrorContext", "description": "Optional. A description of the context in which the error occurred."}, "eventTime": {"description": "Optional. Time when the event occurred. If not provided, the time when the event was received by the Error Reporting system is used. If provided, the time must not exceed the [logs retention period](https://cloud.google.com/logging/quotas#logs_retention_periods) in the past, or be more than 24 hours in the future. If an invalid time is provided, then an error is returned.", "format": "google-datetime", "type": "string"}, "message": {"description": "Required. The error message. If no `context.reportLocation` is provided, the message must contain a header (typically consisting of the exception type name and an error message) and an exception stack trace in one of the supported programming languages and formats. Supported languages are Java, Python, JavaScript, Ruby, C#, PHP, and Go. Supported stack trace formats are: * **Java**: Must be the return value of [`Throwable.printStackTrace()`](https://docs.oracle.com/javase/7/docs/api/java/lang/Throwable.html#printStackTrace%28%29). * **Python**: Must be the return value of [`traceback.format_exc()`](https://docs.python.org/2/library/traceback.html#traceback.format_exc). * **JavaScript**: Must be the value of [`error.stack`](https://github.com/v8/v8/wiki/Stack-Trace-API) as returned by V8. * **Ruby**: Must contain frames returned by [`Exception.backtrace`](https://ruby-doc.org/core-2.2.0/Exception.html#method-i-backtrace). * **C#**: Must be the return value of [`Exception.ToString()`](https://msdn.microsoft.com/en-us/library/system.exception.tostring.aspx). * **PHP**: Must be prefixed with `\"PHP (Notice|Parse error|Fatal error|Warning): \"` and contain the result of [`(string)$exception`](https://php.net/manual/en/exception.tostring.php). * **Go**: Must be the return value of [`debug.Stack()`](https://pkg.go.dev/runtime/debug#Stack).", "type": "string"}, "serviceContext": {"$ref": "ServiceContext", "description": "Required. The service context in which this error has occurred."}}, "type": "object"}, "ServiceContext": {"description": "Describes a running service that sends errors. Its version changes over time and multiple versions can run in parallel.", "id": "ServiceContext", "properties": {"resourceType": {"description": "Type of the MonitoredResource. List of possible values: https://cloud.google.com/monitoring/api/resources Value is set automatically for incoming errors and must not be set when reporting errors.", "type": "string"}, "service": {"description": "An identifier of the service, such as the name of the executable, job, or Google App Engine service name. This field is expected to have a low number of values that are relatively stable over time, as opposed to `version`, which can be changed whenever new code is deployed. Contains the service name for error reports extracted from Google App Engine logs or `default` if the App Engine default service is used.", "type": "string"}, "version": {"description": "Represents the source code version that the developer provided, which could represent a version label or a Git SHA-1 hash, for example. For App Engine standard environment, the version is set to the version of the app.", "type": "string"}}, "type": "object"}, "SourceLocation": {"description": "Indicates a location in the source code of the service for which errors are reported. `functionName` must be provided by the application when reporting an error, unless the error report contains a `message` with a supported exception stack trace. All fields are optional for the later case.", "id": "SourceLocation", "properties": {"filePath": {"description": "The source code filename, which can include a truncated relative path, or a full path from a production machine.", "type": "string"}, "functionName": {"description": "Human-readable name of a function or method. The value can include optional context like the class or package name. For example, `my.package.MyClass.method` in case of Java.", "type": "string"}, "lineNumber": {"description": "1-based. 0 indicates that the line number is unknown.", "format": "int32", "type": "integer"}}, "type": "object"}, "SourceReference": {"description": "A reference to a particular snapshot of the source tree used to build and deploy an application.", "id": "SourceReference", "properties": {"repository": {"description": "Optional. A URI string identifying the repository. Example: \"https://github.com/GoogleCloudPlatform/kubernetes.git\"", "type": "string"}, "revisionId": {"description": "The canonical and persistent identifier of the deployed revision. Example (git): \"0035781c50ec7aa23385dc841529ce8a4b70db1b\"", "type": "string"}}, "type": "object"}, "TimedCount": {"description": "The number of errors in a given time period. All numbers are approximate since the error events are sampled before counting them.", "id": "TimedCount", "properties": {"count": {"description": "Approximate number of occurrences in the given time period.", "format": "int64", "type": "string"}, "endTime": {"description": "End of the time period to which `count` refers (excluded).", "format": "google-datetime", "type": "string"}, "startTime": {"description": "Start of the time period to which `count` refers (included).", "format": "google-datetime", "type": "string"}}, "type": "object"}, "TrackingIssue": {"description": "Information related to tracking the progress on resolving the error.", "id": "TrackingIssue", "properties": {"url": {"description": "A URL pointing to a related entry in an issue tracking system. Example: `https://github.com/user/project/issues/4`", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Error Reporting API", "version": "v1beta1", "version_module": true}