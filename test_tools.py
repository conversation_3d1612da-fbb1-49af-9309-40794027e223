#!/usr/bin/env python3
"""
Test script for AI Assistant Tools
Tests all tools independently to verify functionality
"""

from tools import get_all_tools, get_tools_summary

def test_tools():
    """Test all available tools"""
    print("🧪 Testing AI Assistant Tools")
    print("=" * 50)
    
    # Get all tools
    tools = get_all_tools()
    print(f"Found {len(tools)} tools")
    
    for tool in tools:
        print(f"\n📦 Testing {tool.name.title()} Tool")
        print("-" * 30)
        
        # Get declarations
        declarations = tool.get_declarations()
        print(f"Functions: {[d['name'] for d in declarations]}")
        
        # Test each function
        for declaration in declarations:
            func_name = declaration['name']
            print(f"\n🔧 Testing {func_name}:")
            
            try:
                # Test based on function name
                if func_name == "calculate":
                    result = tool.execute("calculate", expression="2 + 3 * 4")
                    print(f"   calculate('2 + 3 * 4') = {result}")
                
                elif func_name == "convert_units":
                    result = tool.execute("convert_units", value=100, from_unit="celsius", to_unit="fahrenheit")
                    print(f"   convert_units(100, 'celsius', 'fahrenheit') = {result}")
                
                elif func_name == "get_weather":
                    result = tool.execute("get_weather", location="San Francisco, CA")
                    print(f"   get_weather('San Francisco, CA') = {result}")
                
                elif func_name == "get_forecast":
                    result = tool.execute("get_forecast", location="London, UK", days=3)
                    print(f"   get_forecast('London, UK', days=3) = {result}")
                
                elif func_name == "list_files":
                    result = tool.execute("list_files", directory=".")
                    print(f"   list_files('.') = Found {result.get('result', {}).get('total_items', 0)} items")
                
                elif func_name == "read_file":
                    result = tool.execute("read_file", filename="ai_assistant.py", max_lines=5)
                    lines_read = result.get('result', {}).get('lines_read', 0)
                    print(f"   read_file('ai_assistant.py', max_lines=5) = Read {lines_read} lines")
                
                elif func_name == "get_file_info":
                    result = tool.execute("get_file_info", path="ai_assistant.py")
                    file_size = result.get('result', {}).get('size_formatted', 'unknown')
                    print(f"   get_file_info('ai_assistant.py') = Size: {file_size}")
                
                else:
                    print(f"   ⚠️  No test case for {func_name}")
                    
            except Exception as e:
                print(f"   ❌ Error: {e}")
    
    print(f"\n✅ Tool testing completed!")
    
    # Test tools summary
    print(f"\n📊 Tools Summary:")
    summary = get_tools_summary()
    print(f"Total tools: {summary['count']}")
    for tool_info in summary['tools']:
        print(f"  • {tool_info['name']}: {', '.join(tool_info['functions'])}")

if __name__ == "__main__":
    test_tools()
