#!/usr/bin/env python3
"""
Terminal AI Assistant with Gemini Function Calling
Uses Native Gemini API with automatic function calling capabilities
Includes modular tools system for advanced AI interactions
"""

import os
import sys
import time
from typing import List, Dict, Optional
from abc import ABC, abstractmethod

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
    DOTENV_AVAILABLE = True
except ImportError:
    DOTENV_AVAILABLE = False
    print("Info: python-dotenv not available. Install with: pip install python-dotenv")

# Import Gemini API
try:
    import google.generativeai as genai
    GEMINI_AVAILABLE = True
except ImportError:
    GEMINI_AVAILABLE = False
    print("Warning: google.generativeai not available. Install with: pip install google-generativeai")

# OpenAI import removed - using only Gemini API
OPENAI_AVAILABLE = False

# Import tools system
try:
    from tools import get_all_tools, get_tools_summary, get_tool_by_name
    TOOLS_AVAILABLE = True
except ImportError:
    TOOLS_AVAILABLE = False
    print("Warning: Tools system not available. Function calling will be disabled.")


class StreamingInterface(ABC):
    """Abstract interface for different streaming implementations"""

    @abstractmethod
    def stream_response(self, message: str, conversation_history: List[Dict]) -> str:
        """Stream a response and return the complete text"""
        pass


class GeminiNativeStreaming(StreamingInterface):
    """Native Gemini API streaming implementation with function calling"""

    def __init__(self, api_key: str, system_instruction: str = "You are a helpful AI assistant.",
                 tools=None, function_calling_mode: str = "auto"):
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel('gemini-2.0-flash')
        self.system_instruction = system_instruction
        self.tools = tools or []
        self.function_calling_mode = function_calling_mode  # auto, manual, disabled

    def stream_response(self, message: str, conversation_history: List[Dict]) -> str:
        """Stream response using native Gemini API with function calling support"""
        try:
            # Build conversation context
            conversation_parts = []

            # Add system instruction as first message
            if self.system_instruction:
                conversation_parts.append(f"System: {self.system_instruction}")

            # Add conversation history
            for msg in conversation_history[-10:]:  # Keep last 10 messages for context
                conversation_parts.append(f"{msg['role'].title()}: {msg['content']}")

            # Add current user message
            conversation_parts.append(f"User: {message}")

            full_prompt = "\n".join(conversation_parts)

            # Prepare tools for function calling
            tools = None
            if self.tools and self.function_calling_mode == "auto":
                # Convert tools to Python functions for automatic function calling
                python_functions = []
                for tool in self.tools:
                    python_functions.extend(tool.get_python_functions())
                tools = python_functions

            # Generate response with or without tools
            if tools:
                # Use automatic function calling
                response = self.model.generate_content(
                    full_prompt,
                    tools=tools,
                    generation_config=genai.types.GenerationConfig(
                        temperature=0.1,
                        max_output_tokens=1000
                    )
                )

                print("🤖 Assistant: ", end="", flush=True)
                if response.text:
                    # Simulate streaming for consistency
                    for char in response.text:
                        print(char, end="", flush=True)
                        time.sleep(0.01)
                    print()
                    return response.text
                else:
                    print("I've completed the requested action using the available tools.")
                    return "I've completed the requested action using the available tools."

            else:
                # Regular streaming without function calling
                response = self.model.generate_content(
                    full_prompt,
                    generation_config=genai.types.GenerationConfig(
                        temperature=0.7,
                        max_output_tokens=1000
                    ),
                    stream=True
                )

                complete_response = ""
                print("🤖 Assistant: ", end="", flush=True)

                for chunk in response:
                    if chunk.text:
                        print(chunk.text, end="", flush=True)
                        complete_response += chunk.text
                        time.sleep(0.01)

                print()  # New line after response
                return complete_response

        except Exception as e:
            print(f"\n❌ Error with Gemini streaming: {e}")
            import traceback
            traceback.print_exc()
            return ""

    def _handle_manual_function_calls(self, response_text: str, context: str) -> str:
        """Handle manual function calling (simplified implementation)"""
        # This is a simplified implementation for manual function calling
        # In a full implementation, you would parse the response for function calls
        # and execute them following the pattern from function_calling.txt
        return response_text





class TerminalAIAssistant:
    """Main terminal AI assistant with streaming capabilities"""

    def __init__(self):
        self.conversation_history: List[Dict] = []
        self.streaming_client: Optional[StreamingInterface] = None
        self.api_key = self._get_api_key()

    def _get_api_key(self) -> str:
        """Get API key from environment or user input"""
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            api_key = input("Enter your Gemini API key: ").strip()
        return api_key

    def _choose_streaming_method(self) -> bool:
        """Setup Gemini streaming with function calling options"""
        print("\n🚀 Welcome to Terminal AI Assistant with Gemini Function Calling!")

        if not GEMINI_AVAILABLE:
            print("❌ Gemini API not available. Please install google-generativeai:")
            print("   pip install google-generativeai")
            return False

        print("✅ Using Native Gemini API with Advanced Function Calling")
        return self._setup_gemini_streaming()

    def _setup_gemini_streaming(self) -> bool:
        """Setup Gemini streaming with automatic function calling"""
        system_msg = input("\nEnter system instruction (or press Enter for default): ").strip()
        if not system_msg:
            system_msg = "You are a helpful AI assistant with access to various tools. Use them when appropriate to help the user."

        # Temporarily disable function calling to get basic streaming working
        tools = []
        function_calling_mode = "disabled"

        if TOOLS_AVAILABLE:
            tools = get_all_tools()
            print(f"\n🛠️  Found {len(tools)} tools (function calling temporarily disabled):")
            for tool in tools:
                declarations = tool.get_declarations()
                functions = [decl['name'] for decl in declarations]
                print(f"  • {tool.name.title()}: {', '.join(functions)}")
        else:
            print("\n⚠️  No tools available - running in text-only mode")

        self.streaming_client = GeminiNativeStreaming(
            self.api_key, system_msg, tools, function_calling_mode
        )

        print(f"\n✅ Gemini API ready with {len(tools)} tools!")
        print("💡 The AI can now read files, calculate, get weather, and more!")

        return True



    def _show_available_tools(self, tools):
        """Display available tools to the user"""
        print(f"\n🛠️  Available Tools ({len(tools)}):")
        for tool in tools:
            declarations = tool.get_declarations()
            functions = [decl['name'] for decl in declarations]
            print(f"  • {tool.name.title()}: {', '.join(functions)}")
        print()

    def _print_help(self):
        """Print help information"""
        print("\n📖 Commands:")
        print("  /help    - Show this help message")
        print("  /quit    - Exit the assistant")
        print("  /exit    - Exit the assistant")
        print("  /clear   - Clear conversation history")
        print("  /history - Show conversation history")
        if TOOLS_AVAILABLE:
            print("  /tools   - List available tools and functions")
            print("  /toggle-tools - Enable/disable function calling")
        print("  Ctrl+C   - Exit the assistant")
        print()

    def _handle_command(self, user_input: str) -> bool:
        """Handle special commands. Returns True if command was handled."""
        if user_input.lower() in ['/quit', '/exit']:
            print("👋 Goodbye!")
            return True

        elif user_input.lower() == '/help':
            self._print_help()
            return False

        elif user_input.lower() == '/clear':
            self.conversation_history.clear()
            print("🗑️  Conversation history cleared.")
            return False

        elif user_input.lower() == '/history':
            if not self.conversation_history:
                print("📝 No conversation history yet.")
            else:
                print("\n📝 Conversation History:")
                for i, msg in enumerate(self.conversation_history, 1):
                    role_emoji = "👤" if msg['role'] == 'user' else "🤖"
                    print(f"{i}. {role_emoji} {msg['role']}: {msg['content'][:100]}...")
            return False

        elif user_input.lower() == '/tools' and TOOLS_AVAILABLE:
            self._show_tools_info()
            return False

        elif user_input.lower() == '/toggle-tools' and TOOLS_AVAILABLE:
            self._toggle_function_calling()
            return False

        return False

    def _show_tools_info(self):
        """Show detailed information about available tools"""
        if not hasattr(self.streaming_client, 'tools') or not self.streaming_client.tools:
            print("🔧 No tools currently loaded.")
            return

        tools = self.streaming_client.tools
        print(f"\n🛠️  Available Tools ({len(tools)}):")
        print("=" * 50)

        for tool in tools:
            print(f"\n📦 {tool.name.title()} ({tool.category})")
            print(f"   {tool.description}")

            declarations = tool.get_declarations()
            for decl in declarations:
                print(f"\n   🔧 {decl['name']}")
                print(f"      {decl['description']}")

                params = decl.get('parameters', {}).get('properties', {})
                required = decl.get('parameters', {}).get('required', [])

                if params:
                    print("      Parameters:")
                    for param_name, param_info in params.items():
                        req_marker = " (required)" if param_name in required else ""
                        param_type = param_info.get('type', 'unknown')
                        param_desc = param_info.get('description', 'No description')
                        print(f"        • {param_name} ({param_type}){req_marker}: {param_desc}")

        mode = getattr(self.streaming_client, 'function_calling_mode', 'unknown')
        print(f"\n🔄 Function Calling Mode: {mode}")
        print()

    def _toggle_function_calling(self):
        """Toggle function calling on/off"""
        if not hasattr(self.streaming_client, 'function_calling_mode'):
            print("❌ Function calling not supported with current streaming method.")
            return

        current_mode = self.streaming_client.function_calling_mode

        if current_mode == "disabled":
            print("🔧 Function calling is currently disabled.")
            enable = input("Enable function calling? (y/N): ").strip().lower()
            if enable == 'y':
                self.streaming_client.function_calling_mode = "auto"
                if not self.streaming_client.tools:
                    self.streaming_client.tools = get_all_tools()
                print("✅ Function calling enabled (automatic mode)")
            else:
                print("Function calling remains disabled.")
        else:
            print(f"🔧 Function calling is currently enabled ({current_mode} mode).")
            disable = input("Disable function calling? (y/N): ").strip().lower()
            if disable == 'y':
                self.streaming_client.function_calling_mode = "disabled"
                print("✅ Function calling disabled")
            else:
                print("Function calling remains enabled.")

    def run(self):
        """Main chat loop"""
        if not self._choose_streaming_method():
            return

        print("\n💬 Chat started! Type '/help' for commands or start chatting.")
        print("=" * 60)

        try:
            while True:
                # Get user input
                user_input = input("\n👤 You: ").strip()

                if not user_input:
                    continue

                # Handle commands
                if self._handle_command(user_input):
                    break

                # Add user message to history
                self.conversation_history.append({
                    "role": "user",
                    "content": user_input
                })

                # Get streaming response
                start_time = time.time()
                response = self.streaming_client.stream_response(user_input, self.conversation_history)
                end_time = time.time()

                if response:
                    # Add assistant response to history
                    self.conversation_history.append({
                        "role": "assistant",
                        "content": response
                    })

                    # Show timing info
                    duration = end_time - start_time
                    print(f"⏱️  Response time: {duration:.2f}s")

        except KeyboardInterrupt:
            print("\n\n👋 Goodbye!")
        except Exception as e:
            print(f"\n❌ Unexpected error: {e}")


if __name__ == "__main__":
    assistant = TerminalAIAssistant()
    assistant.run()
