#!/usr/bin/env python3
"""
Terminal AI Assistant with Streaming Capabilities and Function Calling
Supports both Native Gemini API and OpenAI-Compatible streaming
Includes modular tools system for function calling
"""

import os
import sys
import time
from typing import List, Dict, Optional
from abc import ABC, abstractmethod

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
    DOTENV_AVAILABLE = True
except ImportError:
    DOTENV_AVAILABLE = False
    print("Info: python-dotenv not available. Install with: pip install python-dotenv")

# Import both streaming libraries
try:
    from google import genai
    from google.genai import types
    GEMINI_AVAILABLE = True
except ImportError:
    GEMINI_AVAILABLE = False
    print("Warning: google.genai not available. Install with: pip install google-generativeai")

try:
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    print("Warning: openai not available. Install with: pip install openai")

# Import tools system
try:
    from tools import get_all_tools, get_tools_summary, get_tool_by_name
    TOOLS_AVAILABLE = True
except ImportError:
    TOOLS_AVAILABLE = False
    print("Warning: Tools system not available. Function calling will be disabled.")


class StreamingInterface(ABC):
    """Abstract interface for different streaming implementations"""

    @abstractmethod
    def stream_response(self, message: str, conversation_history: List[Dict]) -> str:
        """Stream a response and return the complete text"""
        pass


class GeminiNativeStreaming(StreamingInterface):
    """Native Gemini API streaming implementation with function calling"""

    def __init__(self, api_key: str, system_instruction: str = "You are a helpful AI assistant.",
                 tools=None, function_calling_mode: str = "auto"):
        self.client = genai.Client(api_key=api_key)
        self.system_instruction = system_instruction
        self.tools = tools or []
        self.function_calling_mode = function_calling_mode  # auto, manual, disabled

    def stream_response(self, message: str, conversation_history: List[Dict]) -> str:
        """Stream response using native Gemini API with function calling support"""
        try:
            # Build conversation context
            contents = []
            for msg in conversation_history[-10:]:  # Keep last 10 messages for context
                contents.append(f"{msg['role']}: {msg['content']}")
            contents.append(f"user: {message}")

            full_context = "\n".join(contents)

            # Prepare configuration
            config_params = {
                "system_instruction": self.system_instruction,
                "temperature": 0.1 if self.tools else 0.7,  # Lower temp for function calling
                "max_output_tokens": 1000
            }

            # Add tools if available and not disabled
            if self.tools and self.function_calling_mode != "disabled":
                if self.function_calling_mode == "auto":
                    # Use automatic function calling (Python SDK feature)
                    python_functions = []
                    for tool in self.tools:
                        python_functions.extend(tool.get_python_functions())
                    config_params["tools"] = python_functions
                else:
                    # Manual function calling
                    tool_declarations = []
                    for tool in self.tools:
                        tool_declarations.extend(tool.get_declarations())

                    if tool_declarations:
                        tools_obj = types.Tool(function_declarations=tool_declarations)
                        config_params["tools"] = [tools_obj]

            config = types.GenerateContentConfig(**config_params)

            # Handle automatic vs manual function calling
            if self.function_calling_mode == "auto" and self.tools:
                # Automatic function calling - SDK handles everything
                response = self.client.models.generate_content(
                    model="gemini-2.0-flash",
                    contents=[full_context],
                    config=config
                )

                print("🤖 Assistant: ", end="", flush=True)
                if response.text:
                    # Simulate streaming for consistency
                    for char in response.text:
                        print(char, end="", flush=True)
                        time.sleep(0.01)
                    print()
                    return response.text
                else:
                    print("Function executed successfully.")
                    return "I've completed the requested action."

            else:
                # Manual function calling or no tools
                response = self.client.models.generate_content_stream(
                    model="gemini-2.0-flash",
                    contents=[full_context],
                    config=config
                )

                complete_response = ""
                print("🤖 Assistant: ", end="", flush=True)

                for chunk in response:
                    if chunk.text:
                        print(chunk.text, end="", flush=True)
                        complete_response += chunk.text
                        time.sleep(0.01)

                print()  # New line after response

                # Check for function calls in manual mode
                if self.tools and self.function_calling_mode == "manual":
                    return self._handle_manual_function_calls(complete_response, full_context)

                return complete_response

        except Exception as e:
            print(f"\n❌ Error with Gemini Native streaming: {e}")
            return ""

    def _handle_manual_function_calls(self, response_text: str, context: str) -> str:
        """Handle manual function calling (simplified implementation)"""
        # This is a simplified implementation for manual function calling
        # In a full implementation, you would parse the response for function calls
        # and execute them following the pattern from function_calling.txt
        return response_text


class OpenAICompatibleStreaming(StreamingInterface):
    """OpenAI-compatible streaming implementation with function calling"""

    def __init__(self, api_key: str, system_instruction: str = "You are a helpful AI assistant.",
                 tools=None, function_calling_mode: str = "auto"):
        self.client = OpenAI(
            api_key=api_key,
            base_url="https://generativelanguage.googleapis.com/v1beta/openai/"
        )
        self.system_instruction = system_instruction
        self.tools = tools or []
        self.function_calling_mode = function_calling_mode

    def stream_response(self, message: str, conversation_history: List[Dict]) -> str:
        """Stream response using OpenAI-compatible API with function calling support"""
        try:
            # Build messages for OpenAI format
            messages = [{"role": "system", "content": self.system_instruction}]
            messages.extend(conversation_history[-10:])  # Keep last 10 messages
            messages.append({"role": "user", "content": message})

            # Prepare request parameters
            request_params = {
                "model": "gemini-2.0-flash",
                "messages": messages,
                "stream": True,
                "temperature": 0.1 if self.tools else 0.7,
                "max_tokens": 1000
            }

            # Add tools if available (OpenAI format)
            if self.tools and self.function_calling_mode != "disabled":
                openai_tools = self._convert_tools_to_openai_format()
                if openai_tools:
                    request_params["tools"] = openai_tools
                    request_params["tool_choice"] = "auto"

            response = self.client.chat.completions.create(**request_params)

            complete_response = ""
            function_calls = []
            print("🤖 Assistant: ", end="", flush=True)

            for chunk in response:
                if chunk.choices[0].delta.content:
                    content = chunk.choices[0].delta.content
                    print(content, end="", flush=True)
                    complete_response += content
                    time.sleep(0.01)

                # Check for function calls
                if hasattr(chunk.choices[0].delta, 'tool_calls') and chunk.choices[0].delta.tool_calls:
                    function_calls.extend(chunk.choices[0].delta.tool_calls)

            print()  # New line after response

            # Handle function calls if present
            if function_calls and self.tools:
                return self._handle_function_calls(function_calls, messages)

            return complete_response

        except Exception as e:
            print(f"\n❌ Error with OpenAI-compatible streaming: {e}")
            return ""

    def _convert_tools_to_openai_format(self) -> List[Dict]:
        """Convert Gemini tool declarations to OpenAI format"""
        openai_tools = []
        for tool in self.tools:
            declarations = tool.get_declarations()
            for declaration in declarations:
                openai_tool = {
                    "type": "function",
                    "function": declaration
                }
                openai_tools.append(openai_tool)
        return openai_tools

    def _handle_function_calls(self, function_calls: List, messages: List[Dict]) -> str:
        """Handle function calls in OpenAI format"""
        # Simplified implementation - in practice, you would execute the functions
        # and send the results back to the model for a final response
        print("🔧 Function calls detected but not fully implemented in OpenAI-compatible mode")
        return "Function calling is partially supported in OpenAI-compatible mode."


class TerminalAIAssistant:
    """Main terminal AI assistant with streaming capabilities"""

    def __init__(self):
        self.conversation_history: List[Dict] = []
        self.streaming_client: Optional[StreamingInterface] = None
        self.api_key = self._get_api_key()

    def _get_api_key(self) -> str:
        """Get API key from environment or user input"""
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            api_key = input("Enter your Gemini API key: ").strip()
        return api_key

    def _choose_streaming_method(self) -> bool:
        """Let user choose streaming method and function calling options"""
        print("\n🚀 Welcome to Terminal AI Assistant with Function Calling!")
        print("Choose your streaming method:")

        options = []
        if GEMINI_AVAILABLE:
            options.append("1. Native Gemini API Streaming (from text_generation.txt)")
        if OPENAI_AVAILABLE:
            options.append("2. OpenAI-Compatible Streaming (from streaming.txt)")

        if not options:
            print("❌ No streaming libraries available. Please install google-generativeai or openai.")
            return False

        for option in options:
            print(option)

        while True:
            try:
                choice = input("\nEnter your choice (1 or 2): ").strip()

                if choice == "1" and GEMINI_AVAILABLE:
                    return self._setup_gemini_streaming()

                elif choice == "2" and OPENAI_AVAILABLE:
                    return self._setup_openai_streaming()

                else:
                    print("Invalid choice. Please try again.")

            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                return False

    def _setup_gemini_streaming(self) -> bool:
        """Setup Gemini streaming with function calling options"""
        system_msg = input("Enter system instruction (or press Enter for default): ").strip()
        if not system_msg:
            system_msg = "You are a helpful AI assistant."

        # Function calling setup
        tools = []
        function_calling_mode = "disabled"

        if TOOLS_AVAILABLE:
            print("\n🔧 Function Calling Options:")
            print("1. Disabled (text-only responses)")
            print("2. Automatic (SDK handles function execution)")
            print("3. Manual (you handle function calls)")

            fc_choice = input("Choose function calling mode (1-3, default: 1): ").strip()

            if fc_choice == "2":
                function_calling_mode = "auto"
                tools = get_all_tools()
                print(f"✅ Loaded {len(tools)} tools for automatic function calling")
            elif fc_choice == "3":
                function_calling_mode = "manual"
                tools = get_all_tools()
                print(f"✅ Loaded {len(tools)} tools for manual function calling")
            else:
                print("✅ Function calling disabled")

        self.streaming_client = GeminiNativeStreaming(
            self.api_key, system_msg, tools, function_calling_mode
        )
        print("✅ Using Native Gemini API Streaming")

        if tools:
            self._show_available_tools(tools)

        return True

    def _setup_openai_streaming(self) -> bool:
        """Setup OpenAI-compatible streaming with function calling options"""
        system_msg = input("Enter system instruction (or press Enter for default): ").strip()
        if not system_msg:
            system_msg = "You are a helpful AI assistant."

        # Function calling setup (limited support in OpenAI-compatible mode)
        tools = []
        function_calling_mode = "disabled"

        if TOOLS_AVAILABLE:
            enable_fc = input("Enable function calling? (y/N): ").strip().lower()
            if enable_fc == 'y':
                function_calling_mode = "auto"
                tools = get_all_tools()
                print(f"✅ Loaded {len(tools)} tools (limited support in OpenAI-compatible mode)")

        self.streaming_client = OpenAICompatibleStreaming(
            self.api_key, system_msg, tools, function_calling_mode
        )
        print("✅ Using OpenAI-Compatible Streaming")

        if tools:
            self._show_available_tools(tools)

        return True

    def _show_available_tools(self, tools):
        """Display available tools to the user"""
        print(f"\n🛠️  Available Tools ({len(tools)}):")
        for tool in tools:
            declarations = tool.get_declarations()
            functions = [decl['name'] for decl in declarations]
            print(f"  • {tool.name.title()}: {', '.join(functions)}")
        print()

    def _print_help(self):
        """Print help information"""
        print("\n📖 Commands:")
        print("  /help    - Show this help message")
        print("  /quit    - Exit the assistant")
        print("  /exit    - Exit the assistant")
        print("  /clear   - Clear conversation history")
        print("  /history - Show conversation history")
        if TOOLS_AVAILABLE:
            print("  /tools   - List available tools and functions")
            print("  /toggle-tools - Enable/disable function calling")
        print("  Ctrl+C   - Exit the assistant")
        print()

    def _handle_command(self, user_input: str) -> bool:
        """Handle special commands. Returns True if command was handled."""
        if user_input.lower() in ['/quit', '/exit']:
            print("👋 Goodbye!")
            return True

        elif user_input.lower() == '/help':
            self._print_help()
            return False

        elif user_input.lower() == '/clear':
            self.conversation_history.clear()
            print("🗑️  Conversation history cleared.")
            return False

        elif user_input.lower() == '/history':
            if not self.conversation_history:
                print("📝 No conversation history yet.")
            else:
                print("\n📝 Conversation History:")
                for i, msg in enumerate(self.conversation_history, 1):
                    role_emoji = "👤" if msg['role'] == 'user' else "🤖"
                    print(f"{i}. {role_emoji} {msg['role']}: {msg['content'][:100]}...")
            return False

        elif user_input.lower() == '/tools' and TOOLS_AVAILABLE:
            self._show_tools_info()
            return False

        elif user_input.lower() == '/toggle-tools' and TOOLS_AVAILABLE:
            self._toggle_function_calling()
            return False

        return False

    def _show_tools_info(self):
        """Show detailed information about available tools"""
        if not hasattr(self.streaming_client, 'tools') or not self.streaming_client.tools:
            print("🔧 No tools currently loaded.")
            return

        tools = self.streaming_client.tools
        print(f"\n🛠️  Available Tools ({len(tools)}):")
        print("=" * 50)

        for tool in tools:
            print(f"\n📦 {tool.name.title()} ({tool.category})")
            print(f"   {tool.description}")

            declarations = tool.get_declarations()
            for decl in declarations:
                print(f"\n   🔧 {decl['name']}")
                print(f"      {decl['description']}")

                params = decl.get('parameters', {}).get('properties', {})
                required = decl.get('parameters', {}).get('required', [])

                if params:
                    print("      Parameters:")
                    for param_name, param_info in params.items():
                        req_marker = " (required)" if param_name in required else ""
                        param_type = param_info.get('type', 'unknown')
                        param_desc = param_info.get('description', 'No description')
                        print(f"        • {param_name} ({param_type}){req_marker}: {param_desc}")

        mode = getattr(self.streaming_client, 'function_calling_mode', 'unknown')
        print(f"\n🔄 Function Calling Mode: {mode}")
        print()

    def _toggle_function_calling(self):
        """Toggle function calling on/off"""
        if not hasattr(self.streaming_client, 'function_calling_mode'):
            print("❌ Function calling not supported with current streaming method.")
            return

        current_mode = self.streaming_client.function_calling_mode

        if current_mode == "disabled":
            print("🔧 Function calling is currently disabled.")
            enable = input("Enable function calling? (y/N): ").strip().lower()
            if enable == 'y':
                self.streaming_client.function_calling_mode = "auto"
                if not self.streaming_client.tools:
                    self.streaming_client.tools = get_all_tools()
                print("✅ Function calling enabled (automatic mode)")
            else:
                print("Function calling remains disabled.")
        else:
            print(f"🔧 Function calling is currently enabled ({current_mode} mode).")
            disable = input("Disable function calling? (y/N): ").strip().lower()
            if disable == 'y':
                self.streaming_client.function_calling_mode = "disabled"
                print("✅ Function calling disabled")
            else:
                print("Function calling remains enabled.")

    def run(self):
        """Main chat loop"""
        if not self._choose_streaming_method():
            return

        print("\n💬 Chat started! Type '/help' for commands or start chatting.")
        print("=" * 60)

        try:
            while True:
                # Get user input
                user_input = input("\n👤 You: ").strip()

                if not user_input:
                    continue

                # Handle commands
                if self._handle_command(user_input):
                    break

                # Add user message to history
                self.conversation_history.append({
                    "role": "user",
                    "content": user_input
                })

                # Get streaming response
                start_time = time.time()
                response = self.streaming_client.stream_response(user_input, self.conversation_history)
                end_time = time.time()

                if response:
                    # Add assistant response to history
                    self.conversation_history.append({
                        "role": "assistant",
                        "content": response
                    })

                    # Show timing info
                    duration = end_time - start_time
                    print(f"⏱️  Response time: {duration:.2f}s")

        except KeyboardInterrupt:
            print("\n\n👋 Goodbye!")
        except Exception as e:
            print(f"\n❌ Unexpected error: {e}")


if __name__ == "__main__":
    assistant = TerminalAIAssistant()
    assistant.run()
