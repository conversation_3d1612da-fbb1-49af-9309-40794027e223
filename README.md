# 🤖 AI Assistant with Function Calling

A powerful terminal-based AI assistant that supports streaming responses and function calling capabilities using the Gemini API.

## ✨ Features

### 🚀 Dual Streaming Support
- **Native Gemini API Streaming** - Direct integration with Gemini's streaming API
- **OpenAI-Compatible Streaming** - Uses OpenAI-compatible endpoint for broader compatibility

### 🛠️ Function Calling System
- **Automatic Function Calling** - <PERSON><PERSON> handles function execution automatically
- **Manual Function Calling** - Full control over function execution flow
- **Modular Tools Architecture** - Easy to add new tools and capabilities

### 🔧 Built-in Tools
1. **File Reader Tool** - Advanced file reading and analysis
   - `read_file(file_path, max_lines, encoding)` - Read complete file contents
   - `read_multiple_files(file_paths, max_lines_per_file)` - Read multiple files at once
   - `analyze_file_content(file_path, analysis_type)` - Analyze file content and structure
   - `search_in_files(file_paths, search_term)` - Search across multiple files

2. **Calculator Tool** - Mathematical calculations and unit conversions
   - `calculate(expression)` - Evaluate mathematical expressions
   - `convert_units(value, from_unit, to_unit)` - Convert between units

3. **Weather Tool** - Weather information (mock implementation)
   - `get_weather(location, units)` - Current weather conditions
   - `get_forecast(location, days, units)` - Weather forecast

4. **File Manager Tool** - File system operations
   - `list_files(directory, show_hidden)` - List directory contents
   - `read_file(filename, max_lines)` - Read file contents
   - `get_file_info(path)` - Get file/directory information

### 💬 Interactive Commands
- `/help` - Show available commands
- `/tools` - List all available tools and functions
- `/toggle-tools` - Enable/disable function calling
- `/clear` - Clear conversation history
- `/history` - Show conversation history
- `/quit` or `/exit` - Exit the assistant

## 📁 Project Structure

```
/Volumes/Work and Projects/Project/Coding Agent/
├── ai_assistant.py          # Main AI assistant application
├── .env                     # Environment variables (API key)
├── test_tools.py           # Tool testing script
├── demo_file_reader.py     # File reader tool demonstration
├── tools/                  # Tools package
│   ├── __init__.py        # Tool registry and discovery
│   ├── base_tool.py       # Abstract base class for tools
│   ├── file_reader_tool.py # Advanced file reading and analysis
│   ├── calculator_tool.py # Mathematical operations
│   ├── weather_tool.py    # Weather information
│   └── file_manager_tool.py # File system operations
├── text_generation.txt    # Gemini API documentation
├── streaming.txt          # OpenAI-compatible streaming
├── function_calling.txt   # Function calling documentation
└── README.md              # This file
```

## 🚀 Quick Start

### 1. Install Dependencies
```bash
pip install google-generativeai openai python-dotenv
```

### 2. Set Up API Key
Your API key is already configured in `.env`:
```
GEMINI_API_KEY=AIzaSyDLREEXRkp9AD2ljyJ4gyhHLbCFwYbsORw
```

### 3. Run the Assistant
```bash
python3 ai_assistant.py
```

### 4. Choose Your Configuration
1. Select streaming method (Native Gemini or OpenAI-Compatible)
2. Configure function calling mode (Disabled, Automatic, or Manual)
3. Start chatting!

## 🧪 Testing

### Test All Tools
```bash
python3 test_tools.py
```

### Example Interactions
```
👤 You: Read the function_calling.txt file and tell me what it's about
🤖 Assistant: [Uses file reader tool to read and analyze the file]

👤 You: Read multiple files: text_generation.txt and streaming.txt
🤖 Assistant: [Uses file reader to read both files and compare content]

👤 You: What's the weather in San Francisco?
🤖 Assistant: [Uses weather tool to get current conditions]

👤 You: Calculate 15 * 23 + 45
🤖 Assistant: [Uses calculator tool: 15 * 23 + 45 = 390]

👤 You: Search for "function" in all documentation files
🤖 Assistant: [Uses file reader to search across multiple files]

👤 You: /tools
🤖 Assistant: [Shows detailed information about all available tools]
```

## 🔧 Adding New Tools

### 1. Create Tool Class
```python
# tools/my_new_tool.py
from .base_tool import BaseTool

class MyNewTool(BaseTool):
    def __init__(self):
        super().__init__()
        self.name = "my_new_tool"
        self.category = "utility"
        self.description = "Description of what this tool does"

    def get_declarations(self):
        return [{
            "name": "my_function",
            "description": "What this function does",
            "parameters": {
                "type": "object",
                "properties": {
                    "param1": {
                        "type": "string",
                        "description": "Parameter description"
                    }
                },
                "required": ["param1"]
            }
        }]

    def execute(self, function_name, **kwargs):
        if function_name == "my_function":
            # Implement your function logic here
            result = {"message": "Function executed successfully"}
            return self.format_success_response(result, function_name)
        else:
            raise ValueError(f"Unknown function: {function_name}")
```

### 2. Tool Auto-Discovery
The tool will be automatically discovered and loaded by the system. No additional configuration needed!

## 📚 Implementation Details

### Function Calling Modes

1. **Automatic Mode** - Uses Gemini SDK's automatic function calling
   - Functions are executed automatically by the SDK
   - Results are incorporated into responses seamlessly
   - Best for production use

2. **Manual Mode** - Full control over function execution
   - Parse function calls from model responses
   - Execute functions manually
   - Send results back to model
   - Best for debugging and custom workflows

3. **Disabled Mode** - Text-only responses
   - No function calling capabilities
   - Standard chat experience
   - Fastest response times

### Streaming Implementation

Both streaming methods maintain real-time character-by-character display while supporting function calling capabilities.

## 🎯 Key Achievements

✅ **Complete Function Calling System** - Both automatic and manual modes
✅ **Advanced File Reading Tool** - Read single/multiple files, analyze content, search across files
✅ **Modular Tools Architecture** - Easy to extend with new capabilities
✅ **Dual Streaming Support** - Native Gemini and OpenAI-compatible
✅ **Rich Terminal Interface** - Commands, help system, tool management
✅ **Comprehensive Testing** - All tools tested and verified
✅ **Production Ready** - Error handling, validation, user-friendly design

## 🔮 Future Enhancements

- Add more built-in tools (email, calendar, web search)
- Implement tool permissions and security
- Add tool configuration and settings
- Support for custom tool directories
- Integration with external APIs
- Tool marketplace and sharing

---

**Ready to chat with your AI assistant!** 🚀
